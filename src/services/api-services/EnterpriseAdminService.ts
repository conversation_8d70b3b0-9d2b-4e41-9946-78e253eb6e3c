import { BaseApiService } from './BaseApiService';

export interface EnterpriseAdmin {
  id: string;
  enterpriseName: string;
  enterpriseLogo?: string;
  location?: string;
  adminType: 'existing' | 'new';
  advisorId?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  createdAt: string;
  updatedAt: string;
}

export interface EnterpriseAdminsListResponse {
  data: EnterpriseAdmin[];
  total: number;
  page: number;
  limit: number;
}

export interface FetchEnterpriseAdminsParams {
  page?: number;
  limit?: number;
  search?: string;
}

class EnterpriseAdminService extends BaseApiService {
  constructor() {
    super();
  }

  // Create Enterprise Admin
  async createEnterpriseAdmin(formData: FormData) {
    return this.post('/enterprise/create-enterprise', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  // Update Enterprise Admin
  async updateEnterpriseAdmin(id: string, formData: FormData) {
    return this.put(`/enterprise/update-enterprise/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  // Delete Enterprise Admin
  async deleteEnterpriseAdmin(id: string) {
    return this.delete(`/enterprise/delete-enterprise/${id}`);
  }

  // Get Enterprise Admins List
  async getEnterpriseAdminsList(params: FetchEnterpriseAdminsParams = {}) {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.search) queryParams.append('search', params.search);
    
    const queryString = queryParams.toString();
    const url = queryString ? `/enterprise/list?${queryString}` : '/enterprise/list';
    
    return this.get<EnterpriseAdminsListResponse>(url);
  }

  // Get Enterprise Admin by ID
  async getEnterpriseAdminById(id: string) {
    return this.get<{ data: EnterpriseAdmin }>(`/enterprise/get-enterprise/${id}`);
  }

  // Get Enterprise Admin Profile
  async getEnterpriseAdminProfile() {
    return this.get<{ data: EnterpriseAdmin }>('/enterprise/profile');
  }

  // Update Enterprise Admin Status
  async updateEnterpriseAdminStatus(id: string, status: 'active' | 'inactive') {
    return this.patch(`/enterprise/update-status/${id}`, { status });
  }
}

export const enterpriseAdminService = new EnterpriseAdminService();
