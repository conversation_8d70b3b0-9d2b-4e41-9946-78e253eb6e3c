import { Advisor } from "models/entities/Advisor";

export interface AdvisorDTO {
  first_name: string;
  last_name?: string;
  email?: string;
  phone?: string;
  company_name: string;
  company_address?: string;

  last_access?: number,
  no_of_access?: number,
}


export interface AdvisorListResponse {
  data: Advisor[];
  meta: AdvisorListMetaResponse;
}

export interface AdvisorListMetaResponse {
  total: number;
  currentPage: number;
  nextPageUrl: string;
  lastPage: number;
}

export interface AdvisorDetailResponse {
  business_owner: Advisor;
}
