import { createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';
import { enterpriseAdminService } from 'services/enterprise-admin.service';

// Action types
export const CREATE_ENTERPRISE_ADMIN = 'CREATE_ENTERPRISE_ADMIN';
export const UPDATE_ENTERPRISE_ADMIN = 'UPDATE_ENTERPRISE_ADMIN';
export const DELETE_ENTERPRISE_ADMIN = 'DELETE_ENTERPRISE_ADMIN';
export const FETCH_ENTERPRISE_ADMINS = 'FETCH_ENTERPRISE_ADMINS';
export const FETCH_ENTERPRISE_ADMIN_BY_ID = 'FETCH_ENTERPRISE_ADMIN_BY_ID';

// Interfaces
export interface CreateEnterpriseAdminPayload {
  enterprise_name: string;
  enterprise_logo?: File;
  location?: string;
  admin_type: 'existing' | 'new';
  existing_advisor_id?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
}

export interface UpdateEnterpriseAdminPayload extends CreateEnterpriseAdminPayload {
  id: string;
}

export interface FetchEnterpriseAdminsPayload {
  page?: number;
  limit?: number;
  search?: string;
}

// Create Enterprise Admin
export const createEnterpriseAdmin = createAsyncThunk(
  CREATE_ENTERPRISE_ADMIN,
  async (
    payload: CreateEnterpriseAdminPayload & {
      onSuccess?: () => void;
      onError?: (error: string) => void;
    },
    { rejectWithValue }
  ) => {
    try {
      const { onSuccess, onError, ...data } = payload;
      
      const formData = new FormData();
      
      // Add enterprise fields
      formData.append('enterprise_name', data.enterprise_name);
      if (data.location) {
        formData.append('location', data.location);
      }
      if (data.enterprise_logo) {
        formData.append('enterprise_logo', data.enterprise_logo);
      }
      
      // Add admin fields based on type
      if (data.admin_type === 'existing') {
        formData.append('existing_advisor_id', data.existing_advisor_id || '');
      } else {
        formData.append('first_name', data.first_name || '');
        formData.append('last_name', data.last_name || '');
        formData.append('email', data.email || '');
        formData.append('phone', data.phone || '');
      }
      
      const response = await enterpriseAdminService.createEnterpriseAdmin(formData);
      
      toast.success('Enterprise admin created successfully');
      onSuccess?.();
      
      return response.data;
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || 'Failed to create enterprise admin';
      toast.error(errorMessage);
      payload.onError?.(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Update Enterprise Admin
export const updateEnterpriseAdmin = createAsyncThunk(
  UPDATE_ENTERPRISE_ADMIN,
  async (
    payload: UpdateEnterpriseAdminPayload & {
      onSuccess?: () => void;
      onError?: (error: string) => void;
    },
    { rejectWithValue }
  ) => {
    try {
      const { onSuccess, onError, id, ...data } = payload;
      
      const formData = new FormData();
      
      // Add enterprise fields
      formData.append('enterprise_name', data.enterprise_name);
      if (data.location) {
        formData.append('location', data.location);
      }
      if (data.enterprise_logo) {
        formData.append('enterprise_logo', data.enterprise_logo);
      }
      
      // Add admin fields based on type
      if (data.admin_type === 'existing') {
        formData.append('existing_advisor_id', data.existing_advisor_id || '');
      } else {
        formData.append('first_name', data.first_name || '');
        formData.append('last_name', data.last_name || '');
        formData.append('email', data.email || '');
        formData.append('phone', data.phone || '');
      }
      
      const response = await enterpriseAdminService.updateEnterpriseAdmin(id, formData);
      
      toast.success('Enterprise admin updated successfully');
      onSuccess?.();
      
      return response.data;
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || 'Failed to update enterprise admin';
      toast.error(errorMessage);
      payload.onError?.(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Delete Enterprise Admin
export const deleteEnterpriseAdmin = createAsyncThunk(
  DELETE_ENTERPRISE_ADMIN,
  async (
    payload: {
      id: string;
      onSuccess?: () => void;
      onError?: (error: string) => void;
    },
    { rejectWithValue }
  ) => {
    try {
      const { id, onSuccess, onError } = payload;
      
      await enterpriseAdminService.deleteEnterpriseAdmin(id);
      
      toast.success('Enterprise admin deleted successfully');
      onSuccess?.();
      
      return { id };
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || 'Failed to delete enterprise admin';
      toast.error(errorMessage);
      payload.onError?.(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Fetch Enterprise Admins List
export const fetchEnterpriseAdminsList = createAsyncThunk(
  FETCH_ENTERPRISE_ADMINS,
  async (
    payload: FetchEnterpriseAdminsPayload & {
      onSuccess?: (data: any) => void;
      onError?: (error: string) => void;
    },
    { rejectWithValue }
  ) => {
    try {
      const { onSuccess, onError, ...params } = payload;
      
      const response = await enterpriseAdminService.getEnterpriseAdminsList(params);
      
      onSuccess?.(response.data);
      
      return response.data;
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || 'Failed to fetch enterprise admins';
      toast.error(errorMessage);
      payload.onError?.(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Fetch Enterprise Admin by ID
export const fetchEnterpriseAdminById = createAsyncThunk(
  FETCH_ENTERPRISE_ADMIN_BY_ID,
  async (
    payload: {
      id: string;
      onSuccess?: (data: any) => void;
      onError?: (error: string) => void;
    },
    { rejectWithValue }
  ) => {
    try {
      const { id, onSuccess, onError } = payload;
      
      const response = await enterpriseAdminService.getEnterpriseAdminById(id);
      
      onSuccess?.(response.data);
      
      return response.data;
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || 'Failed to fetch enterprise admin';
      toast.error(errorMessage);
      payload.onError?.(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);
