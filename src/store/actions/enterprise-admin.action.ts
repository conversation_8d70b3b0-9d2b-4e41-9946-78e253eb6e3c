import { EnterpriseAdminActionType } from './actions.constants';

// Interfaces
export interface CreateEnterpriseAdminPayload {
  enterprise_name: string;
  enterprise_logo?: File;
  location?: string;
  admin_type: 'existing' | 'new';
  existing_advisor_id?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
}

export interface UpdateEnterpriseAdminPayload extends CreateEnterpriseAdminPayload {
  id: string;
}

export interface FetchEnterpriseAdminsPayload {
  page?: number;
  limit?: number;
  search?: string;
}

export interface EnterpriseAdminCreateActionPayloadType {
  enterpriseAdminCreatePayload: CreateEnterpriseAdminPayload;
  onSuccess: () => void;
  onError?: (error: string) => void;
}

// Create Enterprise Admin
export const createEnterpriseAdmin = (
  payload: EnterpriseAdminCreateActionPayloadType
) => ({
  type: EnterpriseAdminActionType.CREATE_ENTERPRISE_ADMIN,
  payload,
});

// Update Enterprise Admin
export const updateEnterpriseAdmin = (payload: {
  id: string;
  data: CreateEnterpriseAdminPayload;
  onSuccess: () => void;
  onError?: (error: string) => void;
}) => ({
  type: EnterpriseAdminActionType.UPDATE_ENTERPRISE_ADMIN,
  payload,
});

// Delete Enterprise Admin
export const deleteEnterpriseAdmin = (payload: {
  id: string;
  onSuccess: () => void;
  onError?: (error: string) => void;
}) => ({
  type: EnterpriseAdminActionType.DELETE_ENTERPRISE_ADMIN,
  payload,
});

// Fetch Enterprise Admins List
export const fetchEnterpriseAdminsList = (payload: {
  filters: FetchEnterpriseAdminsPayload;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}) => ({
  type: EnterpriseAdminActionType.GET_ENTERPRISE_ADMINS,
  payload,
});

// Fetch Enterprise Admin by ID
export const fetchEnterpriseAdminById = (payload: {
  id: string;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}) => ({
  type: EnterpriseAdminActionType.GET_ENTERPRISE_ADMIN,
  payload,
});
