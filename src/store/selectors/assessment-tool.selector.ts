import { createSelector } from 'reselect';
import { AppState } from 'store';

const assessmentToolStore = (store: AppState) => store.assessmentTool;

export const getAssessmentToolResponse = createSelector(
  [assessmentToolStore],
  (assessmentTool) => assessmentTool.response
);

export const getAssessmentToolLoading = createSelector(
  [assessmentToolStore],
  (assessmentTool) => assessmentTool.loading
);

export const getPaymentLoading = createSelector(
  [assessmentToolStore],
  (assessmentTool) => assessmentTool.payment_loading
);

export const getAssessmentToolStatus = createSelector(
  [assessmentToolStore],
  (assessmentTool) => assessmentTool.progress_status
);

export const getBusinessOwnerProperties = createSelector(
  [assessmentToolStore],
  (assessmentTool) => assessmentTool.business_owner_properties
);

export const getIsPasswordSet = createSelector(
  [assessmentToolStore],
  (assessmentTool) => assessmentTool.is_password_set
);

export const getOpenCount = createSelector(
  [assessmentToolStore],
  (assessmentTool) => assessmentTool.openCount
);

export const getReevaluate = createSelector(
  [assessmentToolStore],
  (assessmentTool) => assessmentTool.reevaluate
);

export const getLastFollowUpDiff = createSelector(
  [assessmentToolStore],
  (assessmentTool) => assessmentTool.lastFollowUpDiff
);

export const getAssessmentToolError = createSelector(
  [assessmentToolStore],
  (assessmentTool) => assessmentTool.error
);

export const getAssessmentToolReEvaluate = createSelector(
  [assessmentToolStore],
  (assessmentTool) => assessmentTool.reevaluate
);
