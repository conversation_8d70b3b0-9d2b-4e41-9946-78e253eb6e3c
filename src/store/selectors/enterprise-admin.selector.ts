import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store';

// Base selector
const selectEnterpriseAdminState = (state: RootState) => state.enterpriseAdmin;

// Enterprise Admins List
export const allEnterpriseAdmins = createSelector(
  [selectEnterpriseAdminState],
  (enterpriseAdminState) => enterpriseAdminState.enterpriseAdmins
);

// Current Enterprise Admin
export const currentEnterpriseAdmin = createSelector(
  [selectEnterpriseAdminState],
  (enterpriseAdminState) => enterpriseAdminState.currentEnterpriseAdmin
);

// Loading state
export const enterpriseAdminLoading = createSelector(
  [selectEnterpriseAdminState],
  (enterpriseAdminState) => enterpriseAdminState.loading
);

// Error state
export const enterpriseAdminError = createSelector(
  [selectEnterpriseAdminState],
  (enterpriseAdminState) => enterpriseAdminState.error
);

// Pagination info
export const enterpriseAdminPagination = createSelector(
  [selectEnterpriseAdminState],
  (enterpriseAdminState) => ({
    total: enterpriseAdminState.total,
    page: enterpriseAdminState.page,
    limit: enterpriseAdminState.limit,
  })
);

// Get enterprise admin by ID
export const getEnterpriseAdminById = (id: string) =>
  createSelector([allEnterpriseAdmins], (enterpriseAdmins) =>
    enterpriseAdmins.find((admin) => admin.id === id)
  );

// Get enterprise admins count
export const enterpriseAdminsCount = createSelector(
  [allEnterpriseAdmins],
  (enterpriseAdmins) => enterpriseAdmins.length
);

// Check if enterprise admins list is empty
export const isEnterpriseAdminsListEmpty = createSelector(
  [allEnterpriseAdmins],
  (enterpriseAdmins) => enterpriseAdmins.length === 0
);
