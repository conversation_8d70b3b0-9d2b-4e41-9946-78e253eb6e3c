import { get } from 'lodash';
import { all, call, put, select, takeLatest } from 'redux-saga/effects';

import { toast } from 'react-toastify';
import { enterpriseAdminService } from 'services/enterprise-admin.service';
import { EnterpriseAdminActionType } from 'store/actions/actions.constants';

import {
  EnterpriseAdminCreateActionPayloadType,
  CreateEnterpriseAdminPayload,
} from 'store/actions/enterprise-admin.action';

import { SagaPayloadType } from 'types/SagaPayload.type';

import {
  addEnterpriseAdmin,
  getEnterpriseAdmins,
  setEnterpriseAdmins,
  setEnterpriseAdminsError,
  setEnterpriseAdminsLoading,
  setEnterpriseAdminsPageData,
  setCreateEnterpriseAdminError,
  setCreateEnterpriseAdminLoading,
  setCreatedEnterpriseAdmin,
  setDeleteEnterpriseAdmin,
  setGetEnterpriseAdminError,
  setGetEnterpriseAdminLoading,
  setUpdateEnterpriseAdmin,
  setUpdateEnterpriseAdminError,
  setUpdateEnterpriseAdminLoading,
} from '../reducers/enterprise-admin.reducer';

export interface UpdateEnterpriseAdminSagaPayloadType {
  id: string;
  data: CreateEnterpriseAdminPayload;
  onSuccess: () => void;
}

interface EnterpriseAdminCreateSagaPayloadType extends SagaPayloadType {
  payload: EnterpriseAdminCreateActionPayloadType;
}

function* enterpriseAdminsFetchSaga(action: any): any {
  const { filters } = action.payload;
  const page = get(filters, ['page']);

  const enterpriseAdminData = yield select((state) => state.enterpriseAdmin);

  const isEnterpriseAdminsPageData = enterpriseAdminData?.pages?.[page]?.ids?.length;
  try {
    if (!isEnterpriseAdminsPageData || filters.search !== undefined) {
      yield put(setEnterpriseAdminsLoading({ loading: true }));
      if (!filters.search) {
        delete filters.search;
      }
      const response = yield call(
        enterpriseAdminService.getEnterpriseAdminsList,
        filters
      );
      yield put(getEnterpriseAdmins(response.data.data));
      yield put(setEnterpriseAdminsPageData({ paginationMeta: response.data }));
      yield put(
        setEnterpriseAdmins({
          ids: response.data.data.map((admin: any) => admin.id),
          loadMore: filters.page,
        })
      );
      yield put(setEnterpriseAdminsLoading({ loading: false }));
    }
  } catch (e: any) {
    yield put(setEnterpriseAdminsError({ error: true, message: e?.message }));
    yield put(setEnterpriseAdminsLoading({ loading: false }));
  }
}

// Helper function to convert file to base64
function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert file to base64 string'));
      }
    };
    reader.onerror = (error) => reject(error);
  });
}

function* enterpriseAdminCreateSaga(
  action: EnterpriseAdminCreateSagaPayloadType
): any {
  const { enterpriseAdminCreatePayload, onSuccess, onError } = action.payload;
  try {
    yield put(setCreateEnterpriseAdminLoading({ loading: true }));

    // // Prepare JSON payload
    // const payload: any = {
    //   ep_name: enterpriseAdminCreatePayload.ep_name,
    // };

    // // Add optional fields
    // if (enterpriseAdminCreatePayload.location) {
    //   payload.location = enterpriseAdminCreatePayload.location;
    // }

    // // Convert logo to base64 if provided
    // if (enterpriseAdminCreatePayload.ep_logo) {
    //   const base64Logo: string = yield call(fileToBase64, enterpriseAdminCreatePayload.ep_logo);
    //   payload.ep_logo = base64Logo;
    // }

    // // Add admin fields based on type
    // if (enterpriseAdminCreatePayload.is_existing_advisor) {
    //   payload.existing_advisor_id = enterpriseAdminCreatePayload.existing_advisor_id;
    // } else {
    //   payload.first_name = enterpriseAdminCreatePayload.first_name;
    //   payload.last_name = enterpriseAdminCreatePayload.last_name;
    //   payload.email = enterpriseAdminCreatePayload.email;
    //   payload.phone = enterpriseAdminCreatePayload.phone;
    // }

    const clone = get.cloneDeep(enterpriseAdminCreatePayload);
    const response = yield call(
      enterpriseAdminService.createEnterpriseAdmin,
      payload
    );

    const enterpriseAdmin = get(response, ['data'], {});
    yield put(setCreatedEnterpriseAdmin({ owner: enterpriseAdmin }));
    yield put(setCreateEnterpriseAdminLoading({ loading: false }));
    toast.success('Enterprise admin created successfully!');
    onSuccess();
  } catch (e: any) {
    yield put(
      setCreateEnterpriseAdminError({
        error: true,
        message: e?.response?.data?.message || 'Failed to create enterprise admin',
        errors: e?.response?.data?.errors,
      })
    );
    if (onError) {
      onError(e?.response?.data?.message || 'Failed to create enterprise admin');
    }
  }
}

function* updateEnterpriseAdminSaga(action: any): any {
  const { onSuccess, onError, data, id } = action.payload;
  try {
    yield put(setUpdateEnterpriseAdminLoading({ loading: true }));

    // Prepare JSON payload
    const payload: any = {
      enterprise_name: data.ep_name,
      admin_type: data.is_existing_advisor ? 'existing' : 'new',
    };

    // Add optional fields
    if (data.location) {
      payload.location = data.location;
    }

    // Convert logo to base64 if provided
    if (data.ep_logo) {
      const base64Logo: string = yield call(fileToBase64, data.ep_logo);
      payload.enterprise_logo = base64Logo;
    }

    // Add admin fields based on type
    if (data.is_existing_advisor) {
      payload.existing_advisor_id = data.existing_advisor_id;
    } else {
      payload.first_name = data.first_name;
      payload.last_name = data.last_name;
      payload.email = data.email;
      payload.phone = data.phone;
    }

    const response = yield call(
      enterpriseAdminService.updateEnterpriseAdmin,
      id,
      payload
    );

    yield put(setUpdateEnterpriseAdmin(response));
    yield put(setUpdateEnterpriseAdminLoading({ loading: false }));
    onSuccess();
    toast.success('Enterprise admin updated successfully');
  } catch (e: any) {
    yield put(
      setUpdateEnterpriseAdminError({
        error: true,
        message: e?.response?.data?.message || 'Failed to update enterprise admin',
        errors: e?.response?.data?.errors,
      })
    );
    if (onError) {
      onError(e?.response?.data?.message || 'Failed to update enterprise admin');
    }
  }
}

function* deleteEnterpriseAdminSaga(action: any): any {
  const { id, onSuccess, onError } = action.payload;
  try {
    yield put(setUpdateEnterpriseAdminLoading({ loading: true }));
    const response = yield call(enterpriseAdminService.deleteEnterpriseAdmin, id);
    yield put(setDeleteEnterpriseAdmin(response));
    yield put(setUpdateEnterpriseAdminLoading({ loading: false }));
    onSuccess();
    toast.success('Enterprise admin deleted successfully');
  } catch (e: any) {
    yield put(
      setUpdateEnterpriseAdminError({
        error: true,
        message: e?.response?.data?.message || 'Failed to delete enterprise admin',
        errors: e?.response?.data?.errors,
      })
    );
    if (onError) {
      onError(e?.response?.data?.message || 'Failed to delete enterprise admin');
    }
  }
}

function* enterpriseAdminFetchSaga(action: any): any {
  const { id, onSuccess, onError } = action.payload;
  try {
    yield put(setGetEnterpriseAdminLoading({ loading: true }));
    const response = yield call(
      enterpriseAdminService.getEnterpriseAdminById,
      id
    );

    yield put(addEnterpriseAdmin(response.data.data));
    yield put(setGetEnterpriseAdminLoading({ loading: false }));
    if (onSuccess) {
      onSuccess(response.data.data);
    }
  } catch (e: any) {
    yield put(setGetEnterpriseAdminError({ 
      error: true, 
      message: e?.response?.data?.message || 'Failed to fetch enterprise admin' 
    }));
    if (onError) {
      onError(e?.response?.data?.message || 'Failed to fetch enterprise admin');
    }
  }
}

export function* enterpriseAdminSagaWatcher() {
  yield all([
    takeLatest(
      EnterpriseAdminActionType.GET_ENTERPRISE_ADMINS,
      enterpriseAdminsFetchSaga
    ),
    takeLatest(
      EnterpriseAdminActionType.GET_ENTERPRISE_ADMIN,
      enterpriseAdminFetchSaga
    ),
    takeLatest(
      EnterpriseAdminActionType.CREATE_ENTERPRISE_ADMIN,
      enterpriseAdminCreateSaga
    ),
    takeLatest(
      EnterpriseAdminActionType.UPDATE_ENTERPRISE_ADMIN,
      updateEnterpriseAdminSaga
    ),
    takeLatest(
      EnterpriseAdminActionType.DELETE_ENTERPRISE_ADMIN,
      deleteEnterpriseAdminSaga
    ),
  ]);
}
