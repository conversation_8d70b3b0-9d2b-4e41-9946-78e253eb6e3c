import { get } from 'lodash';
import { all, call, put, select, takeLatest } from 'redux-saga/effects';

import { toast } from 'react-toastify';
import { enterpriseAdminService } from 'services/enterprise-admin.service';
import { EnterpriseAdminActionType } from 'store/actions/actions.constants';

import {
  EnterpriseAdminCreateActionPayloadType,
  CreateEnterpriseAdminPayload,
} from 'store/actions/enterprise-admin.action';

import { SagaPayloadType } from 'types/SagaPayload.type';

import {
  addEnterpriseAdmin,
  getEnterpriseAdmins,
  setEnterpriseAdmins,
  setEnterpriseAdminsError,
  setEnterpriseAdminsLoading,
  setEnterpriseAdminsPageData,
  setCreateEnterpriseAdminError,
  setCreateEnterpriseAdminLoading,
  setCreatedEnterpriseAdmin,
  setDeleteEnterpriseAdmin,
  setGetEnterpriseAdminError,
  setGetEnterpriseAdminLoading,
  setUpdateEnterpriseAdmin,
  setUpdateEnterpriseAdminError,
  setUpdateEnterpriseAdminLoading,
} from '../reducers/enterprise-admin.reducer';

export interface UpdateEnterpriseAdminSagaPayloadType {
  id: string;
  data: CreateEnterpriseAdminPayload;
  onSuccess: () => void;
}

interface EnterpriseAdminCreateSagaPayloadType extends SagaPayloadType {
  payload: EnterpriseAdminCreateActionPayloadType;
}

function* enterpriseAdminsFetchSaga(action: any): any {
  const { filters } = action.payload;
  const page = get(filters, ['page']);

  const enterpriseAdminData = yield select((state) => state.enterpriseAdmin);

  const isEnterpriseAdminsPageData = enterpriseAdminData?.pages?.[page]?.ids?.length;
  try {
    if (!isEnterpriseAdminsPageData || filters.search !== undefined) {
      yield put(setEnterpriseAdminsLoading({ loading: true }));
      if (!filters.search) {
        delete filters.search;
      }
      const response = yield call(
        enterpriseAdminService.getEnterpriseAdminsList,
        filters
      );
      yield put(getEnterpriseAdmins(response.data.data));
      yield put(setEnterpriseAdminsPageData({ paginationMeta: response.data }));
      yield put(
        setEnterpriseAdmins({
          ids: response.data.data.map((admin: any) => admin.id),
          loadMore: filters.page,
        })
      );
      yield put(setEnterpriseAdminsLoading({ loading: false }));
    }
  } catch (e: any) {
    yield put(setEnterpriseAdminsError({ error: true, message: e?.message }));
    yield put(setEnterpriseAdminsLoading({ loading: false }));
  }
}

function* enterpriseAdminCreateSaga(
  action: EnterpriseAdminCreateSagaPayloadType
): any {
  const { enterpriseAdminCreatePayload, onSuccess, onError } = action.payload;
  try {
    yield put(setCreateEnterpriseAdminLoading({ loading: true }));
    
    // Create FormData for file upload
    const formData = new FormData();
    
    // Add enterprise fields
    formData.append('enterprise_name', enterpriseAdminCreatePayload.enterprise_name);
    if (enterpriseAdminCreatePayload.location) {
      formData.append('location', enterpriseAdminCreatePayload.location);
    }
    if (enterpriseAdminCreatePayload.enterprise_logo) {
      formData.append('enterprise_logo', enterpriseAdminCreatePayload.enterprise_logo);
    }
    
    // Add admin fields based on type
    if (enterpriseAdminCreatePayload.admin_type === 'existing') {
      formData.append('existing_advisor_id', enterpriseAdminCreatePayload.existing_advisor_id || '');
    } else {
      formData.append('first_name', enterpriseAdminCreatePayload.first_name || '');
      formData.append('last_name', enterpriseAdminCreatePayload.last_name || '');
      formData.append('email', enterpriseAdminCreatePayload.email || '');
      formData.append('phone', enterpriseAdminCreatePayload.phone || '');
    }
    
    const response = yield call(
      enterpriseAdminService.createEnterpriseAdmin,
      formData
    );
    
    const enterpriseAdmin = get(response, ['data'], {});
    yield put(setCreatedEnterpriseAdmin({ owner: enterpriseAdmin }));
    yield put(setCreateEnterpriseAdminLoading({ loading: false }));
    toast.success('Enterprise admin created successfully!');
    onSuccess();
  } catch (e: any) {
    yield put(
      setCreateEnterpriseAdminError({
        error: true,
        message: e?.response?.data?.message || 'Failed to create enterprise admin',
        errors: e?.response?.data?.errors,
      })
    );
    if (onError) {
      onError(e?.response?.data?.message || 'Failed to create enterprise admin');
    }
  }
}

function* updateEnterpriseAdminSaga(action: any): any {
  const { onSuccess, onError, data, id } = action.payload;
  try {
    yield put(setUpdateEnterpriseAdminLoading({ loading: true }));
    
    // Create FormData for file upload
    const formData = new FormData();
    
    // Add enterprise fields
    formData.append('enterprise_name', data.enterprise_name);
    if (data.location) {
      formData.append('location', data.location);
    }
    if (data.enterprise_logo) {
      formData.append('enterprise_logo', data.enterprise_logo);
    }
    
    // Add admin fields based on type
    if (data.admin_type === 'existing') {
      formData.append('existing_advisor_id', data.existing_advisor_id || '');
    } else {
      formData.append('first_name', data.first_name || '');
      formData.append('last_name', data.last_name || '');
      formData.append('email', data.email || '');
      formData.append('phone', data.phone || '');
    }

    const response = yield call(
      enterpriseAdminService.updateEnterpriseAdmin,
      id,
      formData
    );

    yield put(setUpdateEnterpriseAdmin({ enterpriseAdmin: response.data }));
    yield put(setUpdateEnterpriseAdminLoading({ loading: false }));
    onSuccess();
    toast.success('Enterprise admin updated successfully');
  } catch (e: any) {
    yield put(
      setUpdateEnterpriseAdminError({
        error: true,
        message: e?.response?.data?.message || 'Failed to update enterprise admin',
        errors: e?.response?.data?.errors,
      })
    );
    if (onError) {
      onError(e?.response?.data?.message || 'Failed to update enterprise admin');
    }
  }
}

function* deleteEnterpriseAdminSaga(action: any): any {
  const { id, onSuccess, onError } = action.payload;
  try {
    yield put(setUpdateEnterpriseAdminLoading({ loading: true }));
    const response = yield call(enterpriseAdminService.deleteEnterpriseAdmin, id);
    yield put(setDeleteEnterpriseAdmin({ enterpriseAdmin: { id } }));
    yield put(setUpdateEnterpriseAdminLoading({ loading: false }));
    onSuccess();
    toast.success('Enterprise admin deleted successfully');
  } catch (e: any) {
    yield put(
      setUpdateEnterpriseAdminError({
        error: true,
        message: e?.response?.data?.message || 'Failed to delete enterprise admin',
        errors: e?.response?.data?.errors,
      })
    );
    if (onError) {
      onError(e?.response?.data?.message || 'Failed to delete enterprise admin');
    }
  }
}

function* enterpriseAdminFetchSaga(action: any): any {
  const { id, onSuccess, onError } = action.payload;
  try {
    yield put(setGetEnterpriseAdminLoading({ loading: true }));
    const response = yield call(
      enterpriseAdminService.getEnterpriseAdminById,
      id
    );

    yield put(addEnterpriseAdmin(response.data.data));
    yield put(setGetEnterpriseAdminLoading({ loading: false }));
    if (onSuccess) {
      onSuccess(response.data.data);
    }
  } catch (e: any) {
    yield put(setGetEnterpriseAdminError({ 
      error: true, 
      message: e?.response?.data?.message || 'Failed to fetch enterprise admin' 
    }));
    if (onError) {
      onError(e?.response?.data?.message || 'Failed to fetch enterprise admin');
    }
  }
}

export function* enterpriseAdminSagaWatcher() {
  yield all([
    takeLatest(
      EnterpriseAdminActionType.GET_ENTERPRISE_ADMINS,
      enterpriseAdminsFetchSaga
    ),
    takeLatest(
      EnterpriseAdminActionType.GET_ENTERPRISE_ADMIN,
      enterpriseAdminFetchSaga
    ),
    takeLatest(
      EnterpriseAdminActionType.CREATE_ENTERPRISE_ADMIN,
      enterpriseAdminCreateSaga
    ),
    takeLatest(
      EnterpriseAdminActionType.UPDATE_ENTERPRISE_ADMIN,
      updateEnterpriseAdminSaga
    ),
    takeLatest(
      EnterpriseAdminActionType.DELETE_ENTERPRISE_ADMIN,
      deleteEnterpriseAdminSaga
    ),
  ]);
}
