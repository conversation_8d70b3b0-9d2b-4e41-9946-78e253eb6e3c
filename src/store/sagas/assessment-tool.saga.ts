import { toast } from 'react-toastify';
import { all, call, put, takeLatest } from 'redux-saga/effects';
import {
  AssessmentToolActionType,
  DocumentActionType,
} from 'store/actions/actions.constants';
import { get } from 'lodash';
import { FileService } from 'services/api-services/FileService';
import {
  AssessmentReEvaluatePayload,
  UpdatePublicAssessmentByTokenDTO,
  UploadDocumentPayload,
} from '../../dtos/assessment-tools.dto';
import { assessmentToolService } from '../../services/api-services/AssessmentToolService';
import {
  addAssessmentData,
  addAssessmentError,
  addAssessmentLoading,
  setReEvaluate,
  updateBusinessOwnerProperty,
} from '../reducers/assessment-tool.reducer';
import { SagaPayloadType } from '../../types/SagaPayload.type';
import {
  FetchBusinessOwnerAssessmentPayload,
  FetchOwnAssessmentPayload,
  FetchPublicAssessmentByTokenPayload,
} from '../actions/assessment-tool.action';
import { AssessmentResponseType } from 'types/enum';

interface FetchOwnAssessmentSagaPayloadType extends SagaPayloadType {
  payload: FetchOwnAssessmentPayload;
}

interface FetchBusinessOwnerAssessmentSagaPayloadType extends SagaPayloadType {
  payload: FetchBusinessOwnerAssessmentPayload;
}

interface FetchPublicAssessmentByTokenSagaPayloadType extends SagaPayloadType {
  payload: FetchPublicAssessmentByTokenPayload;
}

function* fetchOwnAssessment(action: FetchOwnAssessmentSagaPayloadType): any {
  const { onError, ...data } = action.payload;
  try {
    yield put(addAssessmentLoading({ loading: true }));
    const response = yield call(assessmentToolService.fetchOwnAssessment, data);
    yield put(addAssessmentData(response));

    yield put(addAssessmentLoading({ loading: false }));
  } catch (e: any) {
    yield put(addAssessmentError({ message: e?.message }));
    yield put(addAssessmentError({ message: e?.response?.data?.message }));
    toast.error(e?.response?.data?.message);
    if (onError) {
      onError();
    }
  }
}

function* updateOwnAssessment(action: any): any {
  const { onSuccess, ...data } = action.payload;

  try {
    yield put(addAssessmentLoading({ loading: true }));
    const response = yield call(
      assessmentToolService.updateOwnAssessment,
      data
    );

    yield put(addAssessmentData(response));
    yield put(addAssessmentLoading({ loading: false }));
    const businessOwnerProperties = get(
      response,
      ['business_owner_properties'],
      {}
    );
    yield put(
      updateBusinessOwnerProperty({
        business_owner_properties: businessOwnerProperties,
      })
    );
    if(action.payload.submit_type === AssessmentResponseType.DRAFT){
      toast.success('Assessment Saved Successfully');
    } else {
      toast.success('Assessment Completed Successfully');
    }
   
    if (onSuccess) {
      onSuccess();
    }
  } catch (e: any) {
    yield put(addAssessmentError({ message: e?.message }));
  }
}

function* fetchBusinessOwnerAssessment(
  action: FetchBusinessOwnerAssessmentSagaPayloadType
): any {
  const { onError, ...data } = action.payload;
  try {
    yield put(addAssessmentLoading({ loading: true }));
    const response = yield call(
      assessmentToolService.fetchBusinessOwnerAssessment,
      data
    );
    yield put(addAssessmentData(response));

    yield put(addAssessmentLoading({ loading: false }));
  } catch (e: any) {
    yield put(addAssessmentError({ message: e?.message }));
    if (onError) {
      onError();
    }
  }
}

function* updateBusinessOwnerAssessment(action: any): any {
  const { onSuccess, ...data } = action.payload;

  try {
    yield put(addAssessmentLoading({ loading: true }));
    const response = yield call(
      assessmentToolService.updateBusinessOwnerAssessment,
      data
    );
    yield put(addAssessmentData(response));
    yield put(addAssessmentLoading({ loading: false }));
    if(action.payload.submit_type === AssessmentResponseType.DRAFT){
      toast.success('Assessment Saved Successfully');
    } else {
      toast.success('Assessment Submitted Successfully');
    }
    if (onSuccess) {
      onSuccess();
    }
  } catch (e: any) {
    yield put(addAssessmentError({ message: e?.message }));
  }
}

function* fetchPublicAssessmentByToken(
  action: FetchPublicAssessmentByTokenSagaPayloadType
): any {
  const { onError, ...data } = action.payload;
  try {
    yield put(addAssessmentLoading({ loading: true }));
    const response = yield call(
      assessmentToolService.fetchPublicAssessmentByToken,
      data
    );
    yield put(addAssessmentData(response));

    yield put(addAssessmentLoading({ loading: false }));
  } catch (e: any) {
    yield put(addAssessmentError({ message: e?.message }));
    if (onError) {
      onError();
    }
  }
}

function* updatePublicAssessmentByToken(action: any): any {
  const data: UpdatePublicAssessmentByTokenDTO = action.payload;
  try {
    yield put(addAssessmentLoading({ loading: true }));
    const response = yield call(
      assessmentToolService.updatePublicAssessmentByToken,
      data
    );
    yield put(addAssessmentData(response));
    toast.success('Assessment Updated Successfully');
    yield put(addAssessmentLoading({ loading: false }));
  } catch (e: any) {
    yield put(addAssessmentError({ message: e?.message }));
  }
}

function* uploadContinuityDocument(action: any): any {
  const { file, payload, ownerId, onSuccess }: UploadDocumentPayload =
    action.payload;
  try {
    const presignedUrl = yield call(
      FileService.uploadFile,
      file,
      payload,
      ownerId
    );
    if (presignedUrl?.url) {
      onSuccess?.(presignedUrl?.url);
      toast.success('Document Uploaded Successfully');
    }
  } catch (e: any) {
    toast.error(e?.response?.data?.message);
  }
}

function* updateAssessmentReEvaluate(action: any): any {
  const { ownerId, tool, status }: AssessmentReEvaluatePayload = action.payload;
  try {
    const response = yield call(
      assessmentToolService.updateAssessmentReEvaluate,
      {
        ownerId,
        tool,
        status,
      }
    );
    yield put(setReEvaluate({ reevaluate: true }));
    toast.success(response.message);
  } catch (e: any) {
    toast.error(e?.response?.data?.message);
  }
}
function* openCountIncrement(action: any): any {
  const { tool } = action.payload;
  try {
    yield call(assessmentToolService.incrementOpenCount, tool);
  } catch (e: any) {
    yield put(addAssessmentError({ message: e?.response?.data?.message }));
  }
}

export function* assessmentToolSagaWatcher() {
  yield all([
    takeLatest(AssessmentToolActionType.GET_OWN_ASSESSMENT, fetchOwnAssessment),
    takeLatest(
      AssessmentToolActionType.UPDATE_OWN_ASSESSMENT,
      updateOwnAssessment
    ),
    takeLatest(
      AssessmentToolActionType.GET_BUSINESS_OWNER_ASSESSMENT,
      fetchBusinessOwnerAssessment
    ),
    takeLatest(
      AssessmentToolActionType.UPDATE_BUSINESS_OWNER_ASSESSMENT,
      updateBusinessOwnerAssessment
    ),
    takeLatest(
      AssessmentToolActionType.GET_PUBLIC_ASSESSMENT_BY_TOKEN,
      fetchPublicAssessmentByToken
    ),
    takeLatest(
      AssessmentToolActionType.UPDATE_PUBLIC_ASSESSMENT_BY_TOKEN,
      updatePublicAssessmentByToken
    ),
    takeLatest(
      AssessmentToolActionType.UPDATE_ASSESSMENT_RE_EVALUATE,
      updateAssessmentReEvaluate
    ),
    takeLatest(DocumentActionType.UPLOAD_DOCUMENT, uploadContinuityDocument),
    takeLatest(
      AssessmentToolActionType.OPEN_COUNT_INCREMENT,
      openCountIncrement
    ),
  ]);
}
