import { all, fork } from 'redux-saga/effects';
import { authSaga } from './auth.saga';
import { businessOwnerSagaWatcher } from './business-owner.saga';
import { assessmentToolSagaWatcher } from './assessment-tool.saga';
import { forgotPasswordSagaWatcher } from './forgot-password.saga';
import { userSaga } from './user.saga';
import { assessmentReportSagaWatcher } from './asssessment-report.saga';
import { followUpSagaWatcher } from './follow-up.saga';
import { paymentSagaWatcher } from './payment.saga';
import { advisorInviteSagaWatcher } from './advisor-invite.saga';
import { secondaryAdvisorVerifySagaWatcher } from './secondaryAdvisor.saga';
import { toolCommentSagaWatcher } from './tool-comment.saga';
import { documentsSagaWatcher } from './documents.saga';
import { reportBuilderSagaWatcher } from './report-builder.saga';
import { advisorSagaWatcher } from './advisor.saga';

export default function* rootSaga() {
  yield all([
    fork(authSaga),
    fork(businessOwnerSagaWatcher),
    fork(assessmentToolSagaWatcher),
    fork(forgotPasswordSagaWatcher),
    fork(userSaga),
    fork(assessmentReportSagaWatcher),
    fork(followUpSagaWatcher),
    fork(paymentSagaWatcher),
    fork(advisorInviteSagaWatcher),
    fork(secondaryAdvisorVerifySagaWatcher),
    fork(toolCommentSagaWatcher),
    fork(documentsSagaWatcher),
    fork(reportBuilderSagaWatcher),
    fork(advisorSagaWatcher)
  ]);
}
