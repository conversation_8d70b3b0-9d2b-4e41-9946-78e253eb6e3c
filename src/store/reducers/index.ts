import { combineReducers } from 'redux';
import { toast } from 'react-toastify';
import { createBrowserHistory } from 'history';
import { shutdown } from '@intercom/messenger-js-sdk';
import authReducer from './auth.reducer';
import businessOwnerReducer from './business-owner.reducer';
import assessmentToolReducer from './assessment-tool.reducer';
import forgotPasswordReducer from './forgot-password.reducer';
import assessmentReportReducer from './assessment-report.reducer';
import secondaryAdvisorInviteReducer from './advisor-invite.reducer';
import followUpReducer from './follow-up-reducer';
import userReducer from './user.reducer';
import { AuthActionType } from '../actions/actions.constants';
import { localStorageService } from '../../services/LocalStorageService';
import { UserType } from '../../types/enum';
import secondaryAdvisorReducer from './secondary-advisor.reducer';
import toolCommentReducer from './tool-comment.reducer';
import documentsReducer from './documents.reducer';
import reportBuilderReducer from './report-builder.reducer';
import advisorReducer from './advisor.reducer';
import enterpriseAdminReducer from './enterprise-admin.reducer';

export const history = createBrowserHistory();

const appReducer = combineReducers({
  auth: authReducer,
  user: userReducer,
  businessOwner: businessOwnerReducer,
  assessmentTool: assessmentToolReducer,
  forgotPassword: forgotPasswordReducer,
  assessmentReport: assessmentReportReducer,
  followUp: followUpReducer,
  secondaryAdvisorInvite: secondaryAdvisorInviteReducer,
  secondaryAdvisor: secondaryAdvisorReducer,
  toolComment: toolCommentReducer,
  documents: documentsReducer,
  reportBuilder: reportBuilderReducer,
  advisor: advisorReducer,
  enterpriseAdmin: enterpriseAdminReducer,
});

const rootReducer = (state: any, action: any) => {
  if (action.type === AuthActionType.LOGOUT) {
    // check for action type
    // eslint-disable-next-line no-param-reassign
    state = undefined;

    const loggedInUserType =
      localStorageService.getLoggedInUserType() || UserType.ADVISOR;
    localStorageService.removeAuthToken();
    shutdown();
    history.replace(`${window.location.origin}/${loggedInUserType}/login`);
    toast.info('Logged Out');
  }
  return appReducer(state, action);
};

export type AppState = ReturnType<typeof rootReducer>;

export default rootReducer;
