import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  createEnterpriseAdmin,
  updateEnterpriseAdmin,
  deleteEnterpriseAdmin,
  fetchEnterpriseAdminsList,
  fetchEnterpriseAdminById,
} from '../actions/enterprise-admin.action';
import { EnterpriseAdmin, EnterpriseAdminsListResponse } from '../../services/api-services/EnterpriseAdminService';

export interface EnterpriseAdminState {
  enterpriseAdmins: EnterpriseAdmin[];
  currentEnterpriseAdmin: EnterpriseAdmin | null;
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  limit: number;
}

const initialState: EnterpriseAdminState = {
  enterpriseAdmins: [],
  currentEnterpriseAdmin: null,
  loading: false,
  error: null,
  total: 0,
  page: 1,
  limit: 10,
};

const enterpriseAdminSlice = createSlice({
  name: 'enterpriseAdmin',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentEnterpriseAdmin: (state) => {
      state.currentEnterpriseAdmin = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Create Enterprise Admin
    builder
      .addCase(createEnterpriseAdmin.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createEnterpriseAdmin.fulfilled, (state, action) => {
        state.loading = false;
        state.enterpriseAdmins.push(action.payload);
      })
      .addCase(createEnterpriseAdmin.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Update Enterprise Admin
    builder
      .addCase(updateEnterpriseAdmin.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateEnterpriseAdmin.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.enterpriseAdmins.findIndex(
          (admin) => admin.id === action.payload.id
        );
        if (index !== -1) {
          state.enterpriseAdmins[index] = action.payload;
        }
        if (state.currentEnterpriseAdmin?.id === action.payload.id) {
          state.currentEnterpriseAdmin = action.payload;
        }
      })
      .addCase(updateEnterpriseAdmin.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Delete Enterprise Admin
    builder
      .addCase(deleteEnterpriseAdmin.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteEnterpriseAdmin.fulfilled, (state, action) => {
        state.loading = false;
        state.enterpriseAdmins = state.enterpriseAdmins.filter(
          (admin) => admin.id !== action.payload.id
        );
        if (state.currentEnterpriseAdmin?.id === action.payload.id) {
          state.currentEnterpriseAdmin = null;
        }
      })
      .addCase(deleteEnterpriseAdmin.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Fetch Enterprise Admins List
    builder
      .addCase(fetchEnterpriseAdminsList.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchEnterpriseAdminsList.fulfilled, (state, action) => {
        state.loading = false;
        const response = action.payload as EnterpriseAdminsListResponse;
        state.enterpriseAdmins = response.data;
        state.total = response.total;
        state.page = response.page;
        state.limit = response.limit;
      })
      .addCase(fetchEnterpriseAdminsList.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Fetch Enterprise Admin by ID
    builder
      .addCase(fetchEnterpriseAdminById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchEnterpriseAdminById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentEnterpriseAdmin = action.payload.data;
      })
      .addCase(fetchEnterpriseAdminById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, clearCurrentEnterpriseAdmin, setLoading } = enterpriseAdminSlice.actions;
export default enterpriseAdminSlice.reducer;
