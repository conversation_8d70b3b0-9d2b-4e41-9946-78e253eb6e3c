import { format } from 'date-fns';
import { getIn } from 'formik';
import { pick, upperCase } from 'lodash';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { User } from 'models/entities/User';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { getUserData } from 'store/selectors/user.selector';
import { AssessmentToolData } from 'types/business-owner.types';
import {
  AssessmentTools,
  AssessmentToolStatus,
  FollowUpType,
  NonAssessmentToolTabs,
  ReportType,
  RouteKey,
  UserRouteType,
  UserType,
} from 'types/enum';
import { QuestionType } from 'views/TransitionObjectives/Questions';
import { ObjectSchema, string } from 'yup';

export const getSingularPlural = (v: number) => {
  if (v <= 1) {
    return '';
  }
  return 's';
};

export const getDefaultRoute = (user: UserType): string => {
  switch (user) {
    case UserType.BUSINESS_OWNER:
      return `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`;
    case UserType.ADVISOR:
      return `/${UserRouteType.ADVISOR}/${RouteKey.DASHBOARD}`;
      case UserType.ADMIN:
      return `/${UserRouteType.ADMIN}/${RouteKey.DASHBOARD}`;
    default:
      return `/${UserRouteType.ADVISOR}/${RouteKey.DASHBOARD}`;
  }
};

export const getUserRouteType = (user: any): UserRouteType => {

  if(!user){
    user = window.location.pathname.split('/')[1] as UserType;
    // handle business owner route
    if(user === 'business-owner'){
      return UserRouteType.BUSINESS_OWNER;
    }
  }
  
  switch (user) {
    case UserType.BUSINESS_OWNER:
      return UserRouteType.BUSINESS_OWNER;
    case UserType.ADVISOR:
      return UserRouteType.ADVISOR;
      case UserType.ADMIN:
      return UserRouteType.ADMIN;
      default:
        return UserRouteType.ADVISOR;
  }
};



export const titleCase = (input: string): string | null => {
  if (!input) {
    return null;
  }
  return input
    ?.split(' ')
    ?.map((w) => w[0].toUpperCase() + w.substr(1).toLowerCase())
    ?.join(' ');
};

export const capitalizeURL = (input: string | null): string => {
  if (input)
    return input
      .split('-') // Split the string by hyphen
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter of each word
      .join(' ');
  // Join the words with a space
  return '';
};

export function generateUniqueKey(prefix: string): string {
  return `${prefix}-${new Date().getTime()}-${Math.random()}`;
}

export const titleCaseAndRemoveUnderScoreOrHyphen = (input: string): string => {
  if (!input) {
    return '';
  }
  return input
    ?.split(/[_-]/)
    ?.map((w) => w[0].toUpperCase() + w.substr(1).toLowerCase())
    ?.join(' ');
};

export const getKey = (index: number) => index;

export const enumTextToOptionsText = (str: string = ''): string => {
  if (!str) {
    return '';
  }
  const parts = str.split('_');
  const refactored = parts.map((p) =>
    p ? p[0].toUpperCase() + p.slice(1, p.length) : p
  );
  let output = '';
  refactored.forEach((o) => {
    output += `${o} `;
  });
  return output;
};

export const getOptionsFromEnum = (obj: { [key: string]: string }): any[] =>
  Object.entries(obj).map(([, value]) => ({
    label: enumTextToOptionsText(value),
    value,
  }));

export const getFormattedDate = (dateString?: string) => {
  if (!dateString) {
    return '-';
  }

  const date = new Date(dateString);

  if (Number.isNaN(date.getTime())) {
    return '-';
  }

  return date.toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
  });
};
export function getMatchingOptionId(
  questionId: number,
  questions: QuestionType[]
) {
  const question = questions.find((ques) => ques.id === questionId);
  if (question) {
    if (typeof question.answer === 'string') {
      const option = question?.options?.find(
        (opt) => opt.value === question.answer
      );
      if (option) {
        return option.id;
      }
    } else if (Array.isArray(question.answer)) {
      const option = question?.options?.find(
        (opt) => opt.value === question.answer[0]?.key
      );
      if (option) {
        if (option.isLast) {
          return 2.2;
        }
        return option.id;
      }
    }
  }
  return null; // Return null if no matching ID is found
}

export function traceQuestionPath(
  Questions: QuestionType[],
  currentQuestionObj: QuestionType,
  historyStack: number[]
) {
  let percentage = 0;
  const currentId = currentQuestionObj?.id;
  const answer = currentQuestionObj?.answer;
  // isLastStep
  if (
    Questions.find((ques) => ques.id === currentId)?.isLastStep ||
    currentQuestionObj?.options?.filter(
      (option) => option.value === answer?.[0]?.key && option.isLast
    ).length
  ) {
    percentage = 100;
    return percentage;
  }
  // case for choisong NO as first option
  if (Math.floor(currentId) === 2) {
    const questionsWithTwo = Questions.filter(
      (ques) => Math.floor(ques?.id) === 2 || ques.id === 1
    ).map((ques) => ques?.id);
    percentage = Math.floor(
      (questionsWithTwo.indexOf(currentId) / questionsWithTwo.length) * 100
    );
  }
  // case when their is second last option
  else if (currentId === 17) {
    percentage = 90;
    return percentage;
  }
  // case when user choose either Partially-Reduced Time or Yes-Permanently
  else if (
    (Math.floor(currentId) !== 2 && historyStack.includes(3)) ||
    historyStack.includes(4) ||
    currentId === 3 ||
    currentId === 4
  ) {
    // filtering questions that don't have 2 in their ids
    const questionsWithoutTwo = Questions.filter(
      (ques) => Math.floor(ques?.id) !== 2
    ).map((ques) => ques);

    // creating nxt possible ids
    const nextPossibleIds = questionsWithoutTwo.map((ques) => {
      if (
        ques?.options?.filter(
          (option) =>
            option.value === answer?.[0]?.key || option.value === answer
        ).length
      ) {
        const nextID = ques?.options?.filter(
          (option) =>
            option.value === answer?.[0]?.key || option.value === answer
        )[0]?.id;
        return nextID;
      }
      const nextID = ques?.options?.[0]?.id;
      return nextID;
    });

    // removing repeated ids from the nextPossibleIds
    const unRepeatedNextPossibleIds = nextPossibleIds.filter(
      (num, index, self) => self.indexOf(num) === index
    );
    percentage = Math.floor(
      (currentId / unRepeatedNextPossibleIds.length) * 100
    );
  }
  return percentage;
}

export function replaceUnderscoresWithDashes(inputString: string): string {
  return inputString.replace(/_/g, '-');
}
export const valuesTobeUpdated = (initial: any, current: any) => {
  const changedValues: { [key: string]: any } = {};
  Object.keys(current).forEach((key) => {
    if (initial[key] !== current[key]) {
      changedValues[key] = current[key];
    }
  });
  return changedValues;
};

export const isRequiredField = (
  validationSchema: ObjectSchema<any>,
  name: string
) => {
  const schemaDescription = validationSchema.describe();
  const accessor = name.split('.').join('.fields.');
  const field = getIn(schemaDescription.fields, accessor);
  if (!field) {
    return false;
  }
  return field.tests.some((test: any) => test.name === 'required');
};

export const isRequiredFieldDependingOnValidationSchema = (
  validationSchema: ObjectSchema<any>,
  name: string
) => isRequiredField(validationSchema, name);

export const getOrderedAwarenessAssessmentData = (
  businessOwner: BusinessOwner
) => {
  const awarenessData = businessOwner?.assessment_data?.awareness;
  const planDevelopmentData = businessOwner?.assessment_data?.plan_development;

  if (awarenessData || planDevelopmentData) {
    const awarenessEntries = awarenessData ? Object.entries(awarenessData) : [];
    const planDevelopmentEntries = planDevelopmentData
      ? Object.entries(planDevelopmentData)
      : [];
    const assessmentData = [...awarenessEntries, ...planDevelopmentEntries];

    return Object.values(AssessmentTools).map((tool) => {
      const toolData = assessmentData.find(([toolName]) => toolName === tool);
      return {
        toolName: tool,
        toolData: toolData?.[1] as AssessmentToolData,
      };
    });
  }

  return [];
};

export const getOrderedAllStageAssessmentData = (
  businessOwner: BusinessOwner
) => {
  const allStageData = {
    ...businessOwner?.assessment_data?.awareness,
    ...businessOwner?.assessment_data?.plan_development,
  };
  if (allStageData) {
    const allToolsData = Object.entries(allStageData);
    return Object.values(AssessmentTools).map((tool) => {
      const toolData = allToolsData.find(([toolName]) => toolName === tool);
      return {
        toolName: tool,
        toolData: toolData?.at(1) as AssessmentToolData,
      };
    });
  }
  return [];
};

export const getFirstEnabledTab = (businessOwner: BusinessOwner) => {
  const orderedAwarenessData = getOrderedAwarenessAssessmentData(
    businessOwner as BusinessOwner
  );
  const firstEnabledTab = orderedAwarenessData
    .find((tool) => tool?.toolData?.status === AssessmentToolStatus.ENABLE)
    ?.toolName.replace('_', '-') as RouteKey;
  if (firstEnabledTab === RouteKey.READINESS_ASSESSMENT) {
    return RouteKey.READINESS_ASSESSMENT_DASHBOARD;
  }
  return firstEnabledTab;
};

export const apiErrorResponse = (error: any) => {
  if (typeof error.message === 'string') {
    // Special handling for account locked error
    if (error.code === 'E_ACCOUNT_LOCKED') {
      return toast.error(error.message, {
        autoClose: false,
      });
    }
    return toast.error(error.message);
  }
  return (
    error?.errors &&
    error.errors.map((err: any) => toast.error(err.message.replace('_', ' ')))
  );
};

export const convertToTitleCase = (str: string) =>
  str?.replace(/_/g, ' ').replace(/\b\w/g, (c) => c?.toUpperCase());

export const formatDate = (startDate: any, dateformat: string) => {
  if (startDate === null || startDate === undefined || startDate === '') {
    return startDate;
  }
  return format(new Date(startDate), dateformat);
};

export const getFollowUpDescription = (
  type: FollowUpType,
  sentFollowUp: number,
  sentDate: Date
) => {
  const currentDate = new Date();

  // Calculate the difference in milliseconds and explicitly cast it to a number
  const differenceInMilliseconds = +currentDate - +sentDate;

  // Convert milliseconds to days
  const differenceInDays = Math.floor(
    differenceInMilliseconds / (1000 * 60 * 60 * 24)
  );

  if (type === FollowUpType.MANUAL) {
    return `Manual Follow Up has been sent to Business Owner ${
      differenceInDays === 0 ? 'today' : `${differenceInDays} days ago.`
    }`;
  }
  if (sentFollowUp === 1) {
    return `First Automated follow has been sent to both the advisor and business owner ${
      differenceInDays === 0 ? 'today' : `${differenceInDays} days ago.`
    } `;
  }
  if (sentFollowUp === 2) {
    return `Second Automated Follow up has been sent to advisor ${
      differenceInDays === 0 ? 'today' : `${differenceInDays} days ago.`
    } `;
  }
  if (sentFollowUp === 3) {
    return `Third Automated Follow up has been sent to both the advisor and business owner ${
      differenceInDays === 0 ? 'today' : `${differenceInDays} days ago.`
    } `;
  }
  return `Fourth Automated Follow up has been sent to  advisor ${
    differenceInDays === 0 ? 'today' : `${differenceInDays} days ago.`
  } `;
};
export const stringifyNumber = (n: number) => {
  const special = [
    'first',
    'second',
    'third',
    'fourth',
    'fifth',
    'sixth',
    'seventh',
    'eighth',
    'ninth',
    'tenth',
    'eleventh',
    'twelfth',
    'thirteenth',
    'fourteenth',
    'fifteenth',
    'sixteenth',
    'seventeenth',
    'eighteenth',
    'nineteenth',
  ];
  const deca = [
    'twent',
    'thirt',
    'fort',
    'fift',
    'sixt',
    'sevent',
    'eight',
    'ninet',
  ];

  if (n < 20) return special[n];
  if (n % 10 === 0) return `${deca[Math.floor(n / 10) - 2]}ieth`;
  return `${deca[Math.floor(n / 10) - 2]}y-${special[n % 10]}`;
};

export const getStripeMessageByStatus = (status: string) => {
  switch (status) {
    case 'active':
      return `The subscription is in good standing and the most recent payment is successful.`;
    case 'incomplete':
      return `A successful payment needs to be made within 23 hours to activate the subscription. Or the payment requires action, like customer authentication.`;
    case 'incomplete_expired':
      return `The initial payment on the subscription failed and no successful payment was made within 23 hours of creating the subscription.`;
    case 'past_due':
      return `Payment on the latest finalized invoice either failed or wasn't attempted.`;
    case 'canceled':
      return `The subscription has been canceled.`;
    case 'unpaid':
      return `The latest invoice hasn't been paid but the subscription remains in place.`;
    case 'paused':
      return `The subscription has ended its trial period without a default payment method.`;
    default:
      return '';
  }
};

export const thankYouPageContent = (toolName: string,userType: string, businesOwnerName: string) => {

  if(userType=== UserType.BUSINESS_OWNER){
    if(toolName === 'Business Valuation Overview'){
      return `Your ${toolName} Assessment has been completed. Your advisor will be in touch to discuss initial insights on your Business Valuation.`;
    }
    return `Your ${toolName} Assessment has been completed. Your advisor will be in touch to discuss results.`;
  } else {
    return `The  ${toolName} Assessment has been completed for your business owner client, ${businesOwnerName}. You can now view this report in your ExitSmarts dashboard. When you are prepared, please reach out to discuss this report with your business owner client.`;
  }

}

export const passwordSchema = string()
  .min(14, 'Password must be 14 characters long')
  .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .matches(/[a-z]/, 'Password must contain at least one lowercase letter')
  .matches(
    /[^a-zA-Z0-9]/,
    'Password must contain at least one special character'
  )
  .matches(/[0-9]/, 'Password must contain at least one digit')
  .required('Password is required');

export const getInitials = (businessOwner: User) =>
  upperCase(
    (businessOwner?.first_name?.at(0) ?? '') +
      (businessOwner?.last_name?.at(0) ?? '')
  );

export const planDevelopmentTabs = [
  AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING,
  AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES,
  AssessmentTools.FINANCIAL_GAP_ANALYSIS,
  AssessmentTools.ASSET_PROTECTION,
  AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE,
];

export const paymentNeededTabs = [
  AssessmentTools.FINANCIAL_GAP_ANALYSIS,
  AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING,
  AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES,
  AssessmentTools.BUSINESS_CONTINUITY,
  AssessmentTools.ASSET_PROTECTION,
  AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE,
];

export const getReportType = (
  tool: AssessmentTools | NonAssessmentToolTabs
): ReportType => {
  switch (tool) {
    case AssessmentTools.BUSINESS_VALUATION:
      return ReportType.BUSINESS_VALUATION;
    case AssessmentTools.ASSET_PROTECTION:
      return ReportType.ASSET_PROTECTION;
    case NonAssessmentToolTabs.BUSINESS_FINANCIAL_ANALYSIS:
      return ReportType.OTHER_FA;
    default:
      return ReportType.BUSINESS_VALUATION;
  }
};
export const getCurrentTab = (
  tool: AssessmentTools,
  searchParams: URLSearchParams,
  pathSegments?: string[]
) => {
  if (tool === AssessmentTools.TRANSITION_OBJECTIVES) {
    return searchParams.get('questionId') ?? searchParams.get('tab');
  }

  const screenTools = [
    AssessmentTools.ASSET_PROTECTION,
    AssessmentTools.BUSINESS_CONTINUITY,
    AssessmentTools.FINANCIAL_GAP_ANALYSIS,
    AssessmentTools.BUSINESS_VALUATION,
    AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE,
  ];
  if (screenTools.includes(tool)) {
    if (pathSegments?.includes('expense-calculator')) {
      return searchParams.get('tab');
    }
    return searchParams.get('screen');
  }
  return searchParams.get('tab');
};

export const getToolName = (tool: AssessmentTools): string => {
  switch (tool) {
    case AssessmentTools.BUSINESS_VALUATION:
      return 'Valuation and Financial Analysis';
    case AssessmentTools.FINANCIAL_GAP_ANALYSIS:
      return 'Wealth Gap Analysis';
    case AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE:
      return 'Buyer Type-Deal Structure';
    default:
      return convertToTitleCase(tool);
  }
};

export const filterEmptyArrayObjects = (data: any) =>
  Object.entries(data).reduce<any>((filteredData, [key, value]) => {
    if (Array.isArray(value)) {
      const nonEmptyObjects = value
        .filter((obj) => {
          const filteredObj = pick(
            obj,
            Object.keys(obj).filter(
              (key: string) => key !== 'priority' && key !== 'saved'
            )
          );

          return Object.values(filteredObj).some(
            (val) => val != null && String(val).trim() !== '' && val !== false
          );
        })
        .filter((obj) => obj.saved !== false);

      if (nonEmptyObjects.length > 0) {
        filteredData[key] = nonEmptyObjects;
      }
    } else {
      filteredData[key] = value;
    }
    return filteredData;
  }, {});
