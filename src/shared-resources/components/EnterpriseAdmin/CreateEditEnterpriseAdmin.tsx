import { Formik, Form } from 'formik';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import * as yup from 'yup';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import FormikFileInput from 'shared-resources/components/Input/FormikFileInput';
import { FormikSelect } from 'shared-resources/components/Select/FormikSelect';
import { Advisor } from 'models/entities/Advisor';
import { allAdvisors } from 'store/selectors/advisor.selector';
import { fetchAdvisorsList } from 'store/actions/advisor.action';
import { createEnterpriseAdmin, updateEnterpriseAdmin } from 'store/actions/enterprise-admin.action';
import { enterpriseAdminLoading } from 'store/selectors/enterprise-admin.selector';

interface CreateOrEditEnterpriseAdminForm {
  enterprise_name: string;
  enterprise_logo?: File | null;
  location?: string;
  admin_type: 'existing' | 'new';
  existing_advisor_id?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
}

interface CreateOrEditEnterpriseAdminModalProps {
  handleModalVisibility: (value: boolean) => void;
  enterpriseAdmin?: any; // Define proper type based on your enterprise admin entity
  onDeleteClick?: () => void;
  reloadEnterpriseAdmins: () => void;
}

const CreateOrEditEnterpriseAdminModal: React.FC<
  CreateOrEditEnterpriseAdminModalProps
> = (props) => {
  const { handleModalVisibility, enterpriseAdmin, onDeleteClick, reloadEnterpriseAdmins } = props;
  const dispatch = useDispatch();
  
  // State for managing advisor selection
  const [adminType, setAdminType] = useState<'existing' | 'new'>('existing');
  
  // Get advisors list from store
  const advisors = useSelector(allAdvisors);
  const loading = useSelector(enterpriseAdminLoading);

  // Fetch advisors on component mount
  useEffect(() => {
    dispatch(fetchAdvisorsList({
      filters: {},
    }));
  }, [dispatch]);

  // Validation schema
  const validationSchema = yup.object().shape({
    enterprise_name: yup.string().required('Enterprise name is required'),
    enterprise_logo: yup.mixed().nullable(),
    location: yup.string(),
    admin_type: yup.string().oneOf(['existing', 'new']).required(),
    existing_advisor_id: yup.string().when('admin_type', {
      is: 'existing',
      then: (schema) => schema.required('Please select an advisor'),
      otherwise: (schema) => schema.nullable(),
    }),
    first_name: yup.string().when('admin_type', {
      is: 'new',
      then: (schema) => schema.required('First name is required'),
      otherwise: (schema) => schema.nullable(),
    }),
    last_name: yup.string().when('admin_type', {
      is: 'new',
      then: (schema) => schema.required('Last name is required'),
      otherwise: (schema) => schema.nullable(),
    }),
    email: yup.string().when('admin_type', {
      is: 'new',
      then: (schema) => schema.required('Email is required').email('This must be a valid e-mail'),
      otherwise: (schema) => schema.nullable(),
    }),
    phone: yup.string().when('admin_type', {
      is: 'new',
      then: (schema) => schema.required('Phone number is required'),
      otherwise: (schema) => schema.nullable(),
    }),
  });

  const formRef: any = useRef();
  const initialValue: CreateOrEditEnterpriseAdminForm = {
    enterprise_name: enterpriseAdmin?.enterpriseName || '',
    enterprise_logo: null,
    location: enterpriseAdmin?.location || '',
    admin_type: 'existing',
    existing_advisor_id: enterpriseAdmin?.advisorId || '',
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
  };

  // Handle form submission for create and edit
  const handleSubmit = async (values: CreateOrEditEnterpriseAdminForm) => {
    const enterpriseAdminData = {
      enterprise_name: values.enterprise_name,
      enterprise_logo: values.enterprise_logo || undefined,
      location: values.location,
      admin_type: values.admin_type,
      existing_advisor_id: values.existing_advisor_id,
      first_name: values.first_name,
      last_name: values.last_name,
      email: values.email,
      phone: values.phone,
    };

    const onSuccess = () => {
      handleModalVisibility(false);
      reloadEnterpriseAdmins();
    };

    const onError = (error: string) => {
      console.error('Error creating/updating enterprise admin:', error);
    };

    if (enterpriseAdmin) {
      // Update existing enterprise admin
      dispatch(updateEnterpriseAdmin({
        id: enterpriseAdmin.id,
        data: enterpriseAdminData,
        onSuccess,
        onError,
      }));
    } else {
      // Create new enterprise admin
      dispatch(createEnterpriseAdmin({
        enterpriseAdminCreatePayload: enterpriseAdminData,
        onSuccess,
        onError,
      }));
    }
  };

  // Prepare advisor options for select
  const advisorOptions = advisors?.map((advisor: Advisor) => ({
    label: `${advisor.firstName} ${advisor.lastName} (${advisor.email})`,
    value: advisor.id.toString(),
  })) || [];

  return (
    <div className='flex flex-col py-9 px-10'>
      <Formik
        innerRef={formRef}
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values, setFieldValue }) => {
          // Handle admin type changes
          useEffect(() => {
            if (values.admin_type !== adminType) {
              setAdminType(values.admin_type);
              // Clear other fields when switching types
              if (values.admin_type === 'existing') {
                setFieldValue('first_name', '');
                setFieldValue('last_name', '');
                setFieldValue('email', '');
                setFieldValue('phone', '');
              } else {
                setFieldValue('existing_advisor_id', '');
              }
            }
          }, [values.admin_type, adminType, setFieldValue]);

          return (
            <Form>
              <div className='space-y-6'>
                {/* Enterprise Information Section */}
                <div>
                  <h3 className='text-lg font-semibold mb-4'>Enterprise Information</h3>
                  <div className='grid grid-cols-2 gap-x-16 gap-y-4'>
                    <FormikInput
                      asterisk
                      name='enterprise_name'
                      key='enterprise_name'
                      label='Enterprise Name'
                      labelClassName='font-medium leading-6.5'
                    />

                    <FormikInput
                      name='location'
                      key='location'
                      label='Location'
                      labelClassName='font-medium leading-6.5'
                    />
                  </div>

                  <div className='mt-4'>
                    <FormikFileInput
                      name='enterprise_logo'
                      label='Enterprise Logo'
                      accept='image/*'
                      placeholder='Click to upload logo or drag and drop'
                      helperText='Optional. Accepted formats: JPG, PNG, GIF'
                    />
                  </div>
                </div>

                {/* Enterprise Admin Details Section */}
                <div>
                  <h3 className='text-lg font-semibold mb-4'>Enterprise Admin Details</h3>

                  {/* Admin Type Selection */}
                  <div className='mb-4'>
                    <FormikSelect
                      name='admin_type'
                      label='Admin Type'
                      labelClassName='font-medium leading-6.5'
                      options={[
                        { label: 'Choose From Existing Advisor', value: 'existing' },
                        { label: 'Create New Admin', value: 'new' },
                      ]}
                      placeholder='Select admin type'
                      classname='w-full'
                    />
                  </div>

                {/* Existing Advisor Selection */}
                {values.admin_type === 'existing' && (
                  <div className='grid grid-cols-1 gap-4'>
                    <FormikSelect
                      asterisk
                      name='existing_advisor_id'
                      label='Select Advisor'
                      labelClassName='font-medium leading-6.5'
                      options={advisorOptions}
                      placeholder='Choose an advisor'
                      classname='w-full'
                      isSearch={true}
                    />
                  </div>
                )}

                {/* New Admin Creation Fields */}
                {values.admin_type === 'new' && (
                  <div className='grid grid-cols-2 gap-x-16 gap-y-4'>
                    <FormikInput
                      asterisk
                      name='first_name'
                      key='first_name'
                      label='First Name'
                      labelClassName='font-medium leading-6.5'
                    />
                    <FormikInput
                      asterisk
                      name='last_name'
                      key='last_name'
                      label='Last Name'
                      labelClassName='font-medium leading-6.5'
                    />
                    <FormikInput
                      asterisk
                      name='email'
                      key='email'
                      label='Email Address'
                      labelClassName='font-medium leading-6.5'
                      type='email'
                    />
                    <FormikInput
                      asterisk
                      name='phone'
                      key='phone'
                      label='Phone Number'
                      labelClassName='font-medium leading-6.5'
                      type='number'
                    />
                  </div>
                )}
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className='flex justify-between mt-[5rem]'>
              {enterpriseAdmin && onDeleteClick ? (
                <Button
                  type='button'
                  className='px-6 py-2 bg-red-01 border-red-01'
                  onClick={onDeleteClick}
                >
                  Delete
                </Button>
              ) : (
                <div />
              )}
              <div className='flex gap-5'>
                <Button
                  className='px-6 py-2 w-[7.55rem]'
                  type='submit'
                  disabled={loading}
                >
                  {loading ? 'Processing...' : (enterpriseAdmin ? 'Update' : 'Create')}
                </Button>
                <Button
                  className='px-6 py-2'
                  theme='secondary'
                  type='button'
                  onClick={() => {
                    handleModalVisibility(false);
                  }}
                  disabled={loading}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </Form>
        );
        }}
      </Formik>
    </div>
  );
};

export default CreateOrEditEnterpriseAdminModal;
