import { Entity } from '../entity';
import { UserType } from '../../types/enum';

export interface User extends Entity {
  email: string;
  first_name: string;
  last_name?: string;
  name: string;
  phone?: string;
  type: UserType;
  business_name?: string;
  hash?: string;
  created_at_unix?: number;
  status?: boolean;
  company_name?: string;
  company_address?: string;
  mfa_preference?: 'email' | 'totp';
}
