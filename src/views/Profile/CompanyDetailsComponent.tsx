import React, { FC, memo, useState } from 'react';
import { useSelector } from 'react-redux';
import Button from 'shared-resources/components/Button/Button';
import { getUserData } from 'store/selectors/user.selector';
import Modal from 'shared-resources/components/Modal/Modal';

import { getFormattedDate } from 'utils/helpers/Helpers';
import { BusinessOwner } from '../../models/entities/BusinessOwner';
import EditCompanyDetailDailog from './EditCompanyDetailDailog';

type CompanyDetailsComponentProps = {};

const CompanyDetailsComponent: FC<CompanyDetailsComponentProps> = () => {
  const user = useSelector(getUserData) as BusinessOwner;
  const [showDialog, setShowDialog] = useState(false);
  return (
    <div className='bg-white rounded-xl px-12.5 py-10 mt-7'>
      <div className='flex justify-end'>
        <Button
          className='px-6 py-2'
          type='button'
          onClick={() => {
            setShowDialog(true);
          }}
        >
          Edit
        </Button>
      </div>
      <div className='flex gap-20'>
        <div className='w-1/8  flex flex-col gap-8'>
          <h2 className='font-medium'>Company Name :</h2>
          {user?.business_start_date && (
            <h2 className='font-medium'>Business Start Date :</h2>
          )}
          {user?.employee_count && (
            <h2 className='font-medium'>Number Of Employees :</h2>
          )}
        </div>
        <div className='flex flex-col gap-8'>
          <h2 className='font-medium text-blue-01'>{user?.business_name}</h2>
          {user?.business_start_date && (
            <h2 className='font-medium text-blue-01'>
              {getFormattedDate(user?.business_start_date)}
            </h2>
          )}
          {user?.employee_count && (
            <h2 className='font-medium text-blue-01'>{user?.employee_count}</h2>
          )}
        </div>
      </div>
      <Modal
        visible={showDialog}
        title='Edit Company Detail'
        handleVisibility={setShowDialog}
        closeOnOutsideClick
        classname='max-w-[46rem]'
      >
        <EditCompanyDetailDailog
          user={user}
          onClose={() => {
            setShowDialog(false);
          }}
        />
      </Modal>
    </div>
  );
};
export default memo(CompanyDetailsComponent);
