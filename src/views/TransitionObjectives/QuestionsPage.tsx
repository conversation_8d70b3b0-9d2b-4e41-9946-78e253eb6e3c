import React, { FC, memo, useCallback, useEffect, useState } from 'react';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import {
  AssessmentResponseType,
  AssessmentToolProgressStatus,
  UserType,
} from 'types/enum';
import { traceQuestionPath } from 'utils/helpers/Helpers';
import SimpleBar from '../../shared-resources/Bars/SimpleBar';
import {
  ObjectivesForAffectTable,
  ObjectivesForTimeFrameTable,
} from './TransitionObjectiveConfig';
import GenericQuestionComp from './GenericQuestionComp';
import { QuestionType, getQuestions } from './Questions';
import { useSelector } from 'react-redux';
import { getUserData } from 'store/selectors/user.selector';
import { getAssessmentSubmittedAt } from 'store/selectors/assessment-tool.selector';

type QuestionsPageProps = {
  setTab: () => void;
  assessmentToolResponse: {
    [key: number | string]: string | undefined;
  };
  assessmentStatus: string;
  setData: (res: any, type?: AssessmentResponseType) => void;
  isCompleteOwner: boolean;
  searchParams: URLSearchParams;
  setSearchParams: (params: URLSearchParams) => void;
};

const QuestionsPage: FC<QuestionsPageProps> = ({
  setTab,
  assessmentToolResponse,
  assessmentStatus,
  setData,
  isCompleteOwner,
  searchParams,
  setSearchParams,
}) => {
  const Questions = getQuestions(isCompleteOwner);
  const [questions, setQuestions] = useState<QuestionType[]>(Questions);
  const [completionStatus, setCompletionStatus] = useState(0);
  const [nextId, setNextId] = useState(0);
  const [questionNumber, setQuestionNumber] = useState(1);
  const [indentatedQuestionNumber, setIndentatedQuestionNumber] = useState<
    number | undefined
  >(undefined);
  const [historyStack, setHistoryStack] = useState<number[]>([]);
  const [clearValues, setClearValues] = useState(false);
  const loggedInUserData = useSelector(getUserData);

  const getInitialQuestions = (questionsData: QuestionType[]) => {
    if (
      assessmentToolResponse &&
      typeof assessmentToolResponse === 'object' &&
      Object.keys(assessmentToolResponse).length
    ) {
      const { orderedQuestionIds } = assessmentToolResponse;
      const newHistoryStack = Array.isArray(orderedQuestionIds)
        ? orderedQuestionIds.map((qId) => +qId).sort((a, b) => a - b)
        : [];

      setHistoryStack(newHistoryStack);
      if (assessmentToolResponse?.lastID) {
        const lastID = assessmentToolResponse.lastID as any;
        setQuestionNumber(lastID);
      }

      return questionsData.map((question) => ({
        ...question,
        answer: assessmentToolResponse[question.id],
      }));
    }

    return questionsData;
  };

  useEffect(() => {
    setCompletionStatus(
      traceQuestionPath(
        Questions,
        questions.filter((ques) => ques?.id === questionNumber)[0],
        historyStack
      )
    );
  }, [questionNumber, questions, historyStack, isCompleteOwner]);

  useEffect(() => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set('questionId', questionNumber.toString());
    setSearchParams(newParams);
  }, [questionNumber, setSearchParams, searchParams]);

  useEffect(() => {
    if (assessmentToolResponse?.lastID) {
      searchParams.set('questionId', assessmentToolResponse?.lastID);
    }
    setSearchParams(searchParams);
  }, [assessmentToolResponse]);

  useEffect(() => {
    if (assessmentStatus === AssessmentToolProgressStatus.IN_PROGRESS) {
      setQuestions(getInitialQuestions(Questions));
    } else {
      setQuestions(Questions);
    }
  }, [assessmentToolResponse, assessmentStatus, isCompleteOwner]);

  const goToNextQuestion = useCallback(
    (nxtId: number) => {
      if (indentatedQuestionNumber) {
        setHistoryStack((prev) => [
          ...prev,
          questionNumber,
          indentatedQuestionNumber,
        ]);
      } else {
        setHistoryStack((prev) => [...prev, questionNumber]);
      }
      setQuestionNumber(nxtId);
      setIndentatedQuestionNumber(undefined);
    },
    [questionNumber, indentatedQuestionNumber]
  );

  const goToPreviousQuestion = useCallback(() => {
    if (questionNumber !== 1) {
      setHistoryStack((prevHistory) => {
        let newHistory = [...prevHistory];

        const prevId = newHistory.pop();

        if (prevId !== undefined) {
          const wholeNumberPrevId = Math.floor(prevId);

          newHistory = newHistory.filter(
            (id) =>
              !Questions.filter((ques: any) => ques.id === id)[0]
                ?.isIndentated || Math.floor(id) !== wholeNumberPrevId
          );

          if (
            !Questions.filter((ques: any) => ques.id === wholeNumberPrevId)[0]
              ?.isIndentated
          ) {
            setQuestionNumber(prevId);
          } else {
            setQuestionNumber(wholeNumberPrevId);
          }
        }
        return newHistory;
      });
    } else {
      setTab();
    }
    setIndentatedQuestionNumber(undefined);
  }, [questionNumber]);

  const setAnswer = useCallback((questionId: number, answerValue: any) => {
    setQuestions((ques) =>
      ques.map((q) => (q.id === questionId ? { ...q, answer: answerValue } : q))
    );
  }, []);

  const handleFormSubmit = (type: AssessmentResponseType) => {
    type AssessmentResponse = { [key: number | string]: string | undefined };

    const assessmentResponse = questions.reduce<AssessmentResponse>(
      (acc, ques) => {
        acc[ques.id] = ques.answer;
        return acc;
      },
      {} as AssessmentResponse
    );

    // For draft saves, preserve all existing answers from assessmentToolResponse
    // and only remove answers that are explicitly undefined/null in current questions
    if (type === AssessmentResponseType.DRAFT && assessmentToolResponse) {
      // Start with existing assessment data
      const preservedResponse = { ...assessmentToolResponse };

      // Update with current question answers (including undefined to clear answers)
      Object.keys(assessmentResponse).forEach((key) => {
        if (+key) {
          preservedResponse[key] = assessmentResponse[key];
        }
      });

      setData(
        {
          ...preservedResponse,
          lastID: questionNumber,
          orderedQuestionIds: historyStack,
        },
        type
      );
    } else {
      // For complete submissions, only include answers from visited questions
      Object.keys(assessmentResponse).forEach((key) => {
        if (
          +key &&
          ![
            ...(historyStack || []),
            ...(indentatedQuestionNumber ? [questionNumber, indentatedQuestionNumber] : [questionNumber]),
          ].includes(+key)
        ) {
          delete assessmentResponse[key];
        }
      });

      setData(
        {
          ...assessmentResponse,
          lastID: questionNumber,
          orderedQuestionIds: historyStack,
        },
        type
      );
    }
  };

  const validationsForTables = () => {
    const question = questions.filter(
      (ques: any) => ques?.id === questionNumber
    );
    const tableType = question[0]?.tableType;
    const answer = question[0]?.answer;

    // Return true if tableType is undefined or null
    if (!tableType) {
      return true;
    }

    // Return false if the answer is undefined
    if (!answer) {
      return false;
    }

    if (tableType === 'stakeHolders') {
      return true; // Return true or false based on the check
    }

    if (tableType === 'objective') {
      return ObjectivesForTimeFrameTable.every(
        (key) =>
          answer[key]?.objective !== null && answer[key]?.havePlan !== null
      );
    }

    if (tableType === 'affect') {
      return ObjectivesForAffectTable.every(
        (key) => answer[key]?.willInfluence !== null
      );
    }
    return false; // Default return value if tableType is not 'affect', 'objective', or 'stakeHolders'
  };

  return (
    <div className='p-7 bg-white h-[calc(100vh-12.4375rem)] w-full'>
      <div className='flex flex-col justify-between'>
        <div
          id='transition-header'
          className='bg-blue-50 pr-5 mr-6 py-1 flex items-center'
        >
          <div className='w-full'>
            <SimpleBar
              percentage={completionStatus}
              activeBarClassName='!bg-blue-01 !rounded-none'
              containerClassName='h-4'
            />
          </div>
          <h3 className='font-medium text-lg ml-5'>{completionStatus}%</h3>
        </div>
        <div className='h-[calc(100vh-21rem)] overflow-y-auto scrollbar my-1'>
          {questionNumber < Questions.length && (
            <GenericQuestionComp
              clearValues={clearValues}
              setClearValues={setClearValues}
              key={questions[questionNumber]?.id}
              questionObj={
                questions.filter((ques) => ques.id === questionNumber)[0]
              }
              setIndex={(index: number) => {
                setNextId(index);
                const filteredQues = questions.filter(
                  (ques) => ques.id === questionNumber
                )[0];
                const currentQues = questions.filter(
                  (ques) => ques.id === questionNumber
                )[0];
                if (filteredQues.isIndentated || currentQues.isIndentated) {
                  setIndentatedQuestionNumber(index);
                }
              }}
              setAnswer={setAnswer}
            />
          )}
          {indentatedQuestionNumber && (
            <GenericQuestionComp
              clearValues={clearValues}
              setClearValues={setClearValues}
              key={`${(questions[questionNumber]?.id ?? 0) + 1}`}
              questionObj={
                questions.filter(
                  (ques) => ques.id === indentatedQuestionNumber
                )[0]
              }
              setIndex={setNextId}
              setAnswer={setAnswer}
            />
          )}
        </div>
        <div className='px-6 py-2'>
          <BackNextComponent
            backStep={() => {
              goToPreviousQuestion();
              if (questionNumber === 1) {
                searchParams.delete('questionId');
                setSearchParams(searchParams);
              }
            }}
            nextStep={() => {
              setClearValues(true);
              goToNextQuestion(nextId);
              if (
                questions.filter((ques) => ques.id === questionNumber)[0]
                  ?.isLastStep ||
                (questionNumber === 2.2 &&
                  questions.filter((ques) => ques.id === questionNumber)[0]
                    ?.answer?.[0]?.key === 'Yes')
              ) {
                handleFormSubmit(AssessmentResponseType.COMPLETE);
              }
            }}
            buttonText={
              questions.filter((ques) => ques.id === questionNumber)[0]
                ?.isLastStep ||
              (questionNumber === 2.2 &&
                questions.filter((ques) => ques.id === questionNumber)[0]
                  ?.answer?.[0]?.key === 'Yes')
                ? (loggedInUserData?.type === UserType.BUSINESS_OWNER
                    ? 'Submit'
                    : 'Complete')
                : 'Next'
              
            }
            onSaveToDraftClick={() =>
              handleFormSubmit(AssessmentResponseType.DRAFT)
            }
            isLoading={false}
            isNextDisable={
              (!questions.filter((ques) => ques.id === questionNumber)[0]
                ?.isLastStep &&
                (!nextId || nextId === questionNumber)) ||
              !validationsForTables()
            }
          />
        </div>
      </div>
    </div>
  );
};
export default memo(QuestionsPage);
