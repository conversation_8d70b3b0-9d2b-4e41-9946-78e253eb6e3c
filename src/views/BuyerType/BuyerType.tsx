import { BusinessOwner } from 'models/entities/BusinessOwner';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import {
  fetchBusinessOwnerAssessment,
  fetchOwnAssessment,
  updateBusinessOwnerAssessment,
  updateOwnAssessment,
} from 'store/actions/assessment-tool.action';
import { fetchBusinessOwner } from 'store/actions/business-owner.action';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentToolLoading,
  getAssessmentToolReEvaluate,
  getAssessmentToolResponse,
  getAssessmentToolStatus,
  getBusinessOwnerProperties,
} from 'store/selectors/assessment-tool.selector';
import { getUserData } from 'store/selectors/user.selector';
import {
  AssessmentResponseType,
  AssessmentToolProgressStatus,
  AssessmentTools,
  RouteKey,
  UserType,
} from 'types/enum';
import { getUserRouteType } from 'utils/helpers/Helpers';
import ThankYouPage from 'views/layout/ThankYouPage';
import { omit } from 'lodash';
import WithComments from '../../shared-resources/components/ToolComments/WithComments';
import {
  BuyerTypeScreens,
  getScreen,
  numberToScreenObject,
  screenTitles,
  screenToNumberObject,
} from './BuyerTypeConfig';

const BuyerType: React.FC = () => {
  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const user = useSelector(getUserData) as BusinessOwner;
  const assessmentToolStatus = useSelector(getAssessmentToolStatus);
  const dispatch = useDispatch();
  const savedResponse: any = useSelector(getAssessmentToolResponse);
  const isLoading = useSelector(getAssessmentToolLoading);
  const [submitType, setSubmitType] = useState<AssessmentResponseType | null>(
    null
  );
  const [response, setResponse] = useState<any>({});
  const [currentScreen, setCurrentScreen] = useState<{
    screen: BuyerTypeScreens;
  }>({ screen: BuyerTypeScreens.GET_STARTED });
  const assessmentReEvaluate = useSelector(getAssessmentToolReEvaluate);
  const navigate = useNavigate();
  const businessOwnerProperties = useSelector(getBusinessOwnerProperties) as {
    first_name: string;
    last_name: string;
    business_name: string;
  };

  useEffect(() => {
    if (
      assessmentToolStatus === AssessmentToolProgressStatus.COMPLETED &&
      assessmentReEvaluate &&
      !isLoading
    ) {
      dispatch(resetAssessmentData());
    }
  }, [assessmentReEvaluate, user, assessmentToolStatus]);

  useEffect(() => {
    const screen = new URLSearchParams(currentScreen as Record<number, string>);
    setSearchParams(screen);
  }, [currentScreen, setSearchParams, searchParams]);

  // set saved tab if assessment is not completed
  useEffect(() => {
    if (
      assessmentToolStatus !== AssessmentToolProgressStatus.COMPLETED &&
      savedResponse?.saved_screen
    ) {
      setCurrentScreen({
        screen: numberToScreenObject[savedResponse?.saved_screen],
      });
    } else {
      setCurrentScreen({ screen: BuyerTypeScreens.GET_STARTED });
    }
  }, [savedResponse]);

  const handleFetchAssessmentError = () => {
    navigate(`/${getUserRouteType(user?.type)}/${RouteKey.DASHBOARD}`);
  };
  // fetch saved assesment
  useEffect(() => {
    if (user?.type === UserType.ADVISOR && id) {
      dispatch(
        fetchBusinessOwnerAssessment({
          tool: AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE,
          businessOwnerId: +id!,
          onError: handleFetchAssessmentError,
        })
      );
    } else if (user?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchOwnAssessment({
          tool: AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE,
          onError: handleFetchAssessmentError,
        })
      );
    }
  }, []);

  useEffect(() => {
    if (
      assessmentToolStatus !== AssessmentToolProgressStatus.COMPLETED ||
      assessmentReEvaluate
    ) {
      setResponse(savedResponse);
    }
  }, [savedResponse]);

  const handleBack = (newData: any) => {
    const currentScreenNumber = screenToNumberObject[currentScreen.screen];
    let prevScreen = numberToScreenObject[currentScreenNumber - 1];

    // If coming from DEAL_STRUCTURE and buyer was identified, go back to ALREADY_IDENTIFIED
    if (
      currentScreen.screen === BuyerTypeScreens.DEAL_STRUCTURE &&
      response?.[BuyerTypeScreens.ALREADY_IDENTIFIED]?.potential_buyer?.trim()
    ) {
      prevScreen = BuyerTypeScreens.ALREADY_IDENTIFIED;
    }

    setCurrentScreen({ screen: prevScreen });
    if (newData) {
      setResponse({
        ...response,
        [currentScreen.screen]: newData,
      });
    }
  };

  const handleNext = (newData: any, key?: string) => {
    const currentScreenNumber = screenToNumberObject[currentScreen.screen];
    let nextScreen = numberToScreenObject[currentScreenNumber + 1];

    // Skip NOT_IDENTIFIED screens and clear their data if buyer is identified
    if (
      currentScreen.screen === BuyerTypeScreens.ALREADY_IDENTIFIED &&
      newData?.potential_buyer?.trim()
    ) {
      nextScreen = BuyerTypeScreens.DEAL_STRUCTURE;
      setResponse({
        ...omit(response, [
          BuyerTypeScreens.NOT_IDENTIFIED_TABLE,
          BuyerTypeScreens.NOT_IDENTIFIED,
        ]),
        [BuyerTypeScreens.ALREADY_IDENTIFIED]: newData,
      });
      setCurrentScreen({ screen: nextScreen });
      return;
    }

    if (key) {
      setResponse({
        ...response,
        [key]: newData,
      });
    }
    setCurrentScreen({ screen: nextScreen });
  };

  const handleSubmit = (
    newData: any,
    key?: string,
    isSubmit?: boolean,
    savedScreen?: number
  ) => {
    let updatedData = {
      ...response,
      saved_screen:
        response?.saved_screen > currentScreen.screen
          ? response?.saved_screen
          : currentScreen.screen,
    };
    if (key) {
      updatedData = {
        ...updatedData,
        [key]: newData,
      };
    }
    if (savedScreen) {
      updatedData = {
        ...updatedData,
        saved_screen: savedScreen,
      };
    }
    if (user?.type === UserType.ADVISOR && id) {
      dispatch(
        updateBusinessOwnerAssessment({
          businessOwnerId: +id!,
          tool: AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE,
          assessment_response: updatedData,
          submit_type: isSubmit
            ? AssessmentResponseType.COMPLETE
            : AssessmentResponseType.DRAFT,
        })
      );
    } else {
      dispatch(
        updateOwnAssessment({
          tool: AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE,
          assessment_response: updatedData,
          submit_type: isSubmit
            ? AssessmentResponseType.SUBMIT
            : AssessmentResponseType.DRAFT,
          onSuccess: () =>
            isSubmit &&
            navigate(`${UserType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`),
        })
      );
    }
    setSubmitType(
      isSubmit ? AssessmentResponseType.COMPLETE : AssessmentResponseType.DRAFT
    );
  };

  useEffect(() => {
    if (id) dispatch(fetchBusinessOwner(id));
  }, [dispatch, id]);

  if (isLoading && !submitType) {
    return <Spinner />;
  }

  if (assessmentToolStatus === AssessmentToolProgressStatus.COMPLETED) {
    return (
      <ThankYouPage
        pageContent={thankYouPageContent(
          'Buyer Type-Deal Structure',
          user?.type || UserType.BUSINESS_OWNER,
          `${businessOwnerProperties?.first_name ?? ''} ${
            businessOwnerProperties?.last_name ?? ''
          }`
        )}
        loggedInUserData={user}
        isPasswordSet={false}
      />
    );
  }

  return (
    <>
      <h1 className='font-bold text-2xl mb-3'>
        {screenTitles[currentScreen.screen]}
      </h1>
      <WithComments tool={AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE}>
        <div className='pr-3 flex flex-col justify-between bg-white h-[calc(100vh-13rem)] w-full'>
          {getScreen(
            currentScreen.screen!,
            response,
            handleBack,
            handleNext,
            handleSubmit
          )}
        </div>
      </WithComments>
    </>
  );
};

export default BuyerType;
