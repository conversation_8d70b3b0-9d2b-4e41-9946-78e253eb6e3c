import React from 'react';
import { Formik, Form } from 'formik';
import { useSelector } from 'react-redux';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import cx from 'classnames';
import * as yup from 'yup';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import { isRequiredFieldDependingOnValidationSchema } from 'utils/helpers/Helpers';
import { BuyerTypeScreens } from './BuyerTypeConfig';

interface Props {
  data: any;
  handleNextClick: (values: any) => void;
  handlebackClick: (values: any) => void;
  handleSaveAsDraftClick: (values: any) => void;
}

const DealStructure2: React.FC<Props> = ({
  data,
  handleNextClick,
  handleSaveAsDraftClick,
  handlebackClick,
}) => {
  const isLoading = useSelector(getAssessmentToolLoading);

  const validationSchema = yup.object().shape({
    engaged_after_sale: yup.string(),
    pending_legal_issues: yup.string(),
    quick_sale_reason: yup.string(),
    assets_not_for_sale: yup.string(),
    liabilities_at_sale: yup.string(),
    willing_to_finance: yup.string(),
    deal_structure: yup.string(),
  });

  const initialValues = {
    engaged_after_sale:
      data?.[BuyerTypeScreens.DEAL_STRUCTURE_2]?.engaged_after_sale ||
      undefined,
    pending_legal_issues:
      data?.[BuyerTypeScreens.DEAL_STRUCTURE_2]?.pending_legal_issues ||
      undefined,
    quick_sale_reason:
      data?.[BuyerTypeScreens.DEAL_STRUCTURE_2]?.quick_sale_reason || undefined,
    assets_not_for_sale:
      data?.[BuyerTypeScreens.DEAL_STRUCTURE_2]?.assets_not_for_sale ||
      undefined,
    liabilities_at_sale:
      data?.[BuyerTypeScreens.DEAL_STRUCTURE_2]?.liabilities_at_sale ||
      undefined,
    willing_to_finance:
      data?.[BuyerTypeScreens.DEAL_STRUCTURE_2]?.willing_to_finance ||
      undefined,
    deal_structure:
      data?.[BuyerTypeScreens.DEAL_STRUCTURE_2]?.deal_structure || undefined,
  };

  if (isLoading) {
    return <Spinner spinnerTheme='overlaySpinner' />;
  }

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={(values) => {
        handleNextClick(values);
      }}
      enableReinitialize
    >
      {({ values }) => (
        <Form
          className={cx(
            'flex flex-col justify-between h-[calc(100vh-11.8rem)] pb-10 pr-10'
          )}
        >
          <div className='overflow-auto scrollbar mt-10 rounded-lg px-10 flex flex-col space-y-6 pb-8'>
            <FormikInput
              name='engaged_after_sale'
              label='Are you willing to stay engaged in the business for a period of time after the sale?'
              labelClassName='mb-1  font-medium'
              fieldType='textarea'
              className='h-28'
            />
            <FormikInput
              name='pending_legal_issues'
              label='Are there any pending legal issues that could affect the sale?'
              labelClassName='mb-1  font-medium'
              fieldType='textarea'
              className='h-28'
            />
            <FormikInput
              name='quick_sale_reason'
              label='Are there any reasons why you need to sell the business quickly?'
              labelClassName='mb-1 font-medium'
              fieldType='textarea'
              className='h-28'
            />
            <FormikInput
              name='assets_not_for_sale'
              label='Are there any assets that you do not want to sell with the business?'
              labelClassName='mb-1 font-medium'
              fieldType='textarea'
              className='h-28'
            />
            <FormikInput
              name='liabilities_at_sale'
              label='What is your expectation regarding any business liabilities at the time of sale?'
              labelClassName='mb-1 font-medium'
              fieldType='textarea'
              className='h-28'
            />
            <FormikInput
              name='willing_to_finance'
              label='Are you willing to finance any part of the purchase price?'
              labelClassName='mb-1 font-medium'
              fieldType='textarea'
              className='h-28'
              asterisk={isRequiredFieldDependingOnValidationSchema(
                validationSchema,
                'willing_to_finance'
              )}
            />
            <FormikInput
              name='deal_structure'
              label='Do you have a particular deal structure in mind?'
              labelClassName='mb-1  font-medium'
              fieldType='textarea'
              className='h-28'
            />
          </div>
          <div className='px-6 py-6'>
            <BackNextComponent
              backStep={() => handlebackClick(values)}
              buttonType='submit'
              buttonText='Submit'
              isLoading={isLoading}
              isNextDisable={false}
              onSaveToDraftClick={() => handleSaveAsDraftClick(values)}
            />
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default DealStructure2;
