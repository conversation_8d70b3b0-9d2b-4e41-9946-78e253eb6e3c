import React, { FC, memo, useEffect, useState } from 'react';
import { BiPlus } from 'react-icons/bi';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import Button from 'shared-resources/components/Button/Button';
import { SelectOption } from 'shared-resources/components/Select/Select';
import {
  AssessmentResponseType,
  StakeHolderAlignmentMeetingTabs,
} from 'types/enum';
import { TableValueType, shareHoldersQuestions } from './Questions';
import StakeholdersTableComp from './StakeholdersTableComp';

type QuestionAnswerPageProps = {
  attendeeTableValue: TableValueType[];
  setCurrentTab: (tab: StakeHolderAlignmentMeetingTabs) => void;
  handleTableValues: (
    id: number,
    field: string,
    value: string | boolean | number
  ) => void;
  addStakeholder: () => void;
  handleDelete: (id: number) => void;
  textAreaValues: { [id: string]: string };
  handleTextAreaValues: (vals: { [id: string]: string }) => void;
  handleSubmitData: (type: AssessmentResponseType) => void;
  isLoading: boolean;
  options: SelectOption[];
};

const QuestionAnswerPage: FC<QuestionAnswerPageProps> = ({
  attendeeTableValue,
  setCurrentTab,
  handleTableValues,
  handleDelete,
  addStakeholder,
  textAreaValues,
  handleTextAreaValues,
  handleSubmitData,
  isLoading,
  options,
}) => {
  const [textAreaAnswers, setTextAreaAnswers] = useState<{
    [id: string]: string;
  }>(textAreaValues);
  const [submitType, setSubmitType] = useState<AssessmentResponseType>(
    AssessmentResponseType.DRAFT
  );

  const handleInputChange = (value: string, id: string) => {
    setTextAreaAnswers((prevState) => ({
      ...prevState,
      [id]: value,
    }));
  };

  useEffect(() => {
    handleTextAreaValues(textAreaAnswers);
  }, [textAreaAnswers]);

  return (
    <div className='overflow-auto scrollbar  '>
      <div className='mr-4 flex flex-col gap-10 relative'>
        {' '}
        {shareHoldersQuestions.map((questionObj, index) => {
          if (questionObj.questionType === 'textArea') {
            return (
              <div key={questionObj?.id}>
                {' '}
                <div className='flex'>
                  {' '}
                  <h1>
                    <strong>{`${index + 1}.  ${
                      questionObj?.question
                    } : `}</strong>
                    {questionObj?.subText}
                  </h1>
                </div>
                <textarea
                  value={textAreaAnswers[questionObj?.id] ?? ''}
                  id='specify'
                  placeholder={questionObj?.placeholder}
                  rows={7}
                  className='mt-2 w-full border-2 border-gray-300 focus:outline-none rounded-md p-4 resize-none'
                  onChange={(e) => {
                    handleInputChange(
                      e.target.value,
                      questionObj?.id as string
                    );
                  }}
                />
              </div>
            );
          }
          return (
            <div key={questionObj?.id}>
              {' '}
              <div className='flex justify-between items-center'>
                {' '}
                <h1 className='font-bold'>{`${index + 1}.  ${
                  questionObj?.question
                } :`}</h1>
                <Button
                  LeadingIcon={BiPlus}
                  leadingIconClassName='w-5 h-5 mr-1'
                  className='px-4 py-2'
                  onClick={addStakeholder}
                >
                  Add Participant
                </Button>
              </div>
              <div className='mt-6'>
                <StakeholdersTableComp
                  isStakeHolder={false}
                  handleDelete={handleDelete}
                  handleInputChange={handleTableValues}
                  tableValues={attendeeTableValue}
                  options={options}
                />
              </div>
            </div>
          );
        })}
        <BackNextComponent
          backStep={() => {
            setCurrentTab(StakeHolderAlignmentMeetingTabs.STAKEHOLDERS);
          }}
          nextStep={() => {
            handleSubmitData(AssessmentResponseType.COMPLETE);
            setSubmitType(AssessmentResponseType.COMPLETE);
          }}
          buttonType='submit'
          buttonText='Submit'
          isLoading={submitType === AssessmentResponseType.DRAFT && isLoading}
          isNextDisable={!attendeeTableValue.length}
          onSaveToDraftClick={() => {
            handleSubmitData(AssessmentResponseType.DRAFT);
            setSubmitType(AssessmentResponseType.DRAFT);
          }}
        />
      </div>
    </div>
  );
};
export default memo(QuestionAnswerPage);
