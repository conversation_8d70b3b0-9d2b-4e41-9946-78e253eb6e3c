import { Form, Formik } from 'formik';
import React from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { FinancialGapAnalysisScreens } from 'types/enum';
import * as yup from 'yup';
import { formatCurrency } from './ExpenseCalculator/ExpenseCalculatorConfig';
import {
  BusinessSaleCostskeys,
  PostExitFinancesKeys,
  getKeyTitle,
} from './FinancialGapAnalysisConfig';

interface Props {
  nextStep: (data: any) => void;
  backStep: () => void;
  data: any;
  onSaveToDraftClick: (data: any) => void;
}
const BusinessSaleCosts: React.FC<Props> = (props) => {
  const { nextStep, backStep, data, onSaveToDraftClick } = props;
  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    [BusinessSaleCostskeys.FEDERAL_TAX_RATE]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [BusinessSaleCostskeys.STATE_TAX_RATE]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [BusinessSaleCostskeys.TRANSACTION_EXPENSE_PERCENTAGE_COSTS]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [BusinessSaleCostskeys.TRANSACTION_EXPENSE_DOLLARS]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [BusinessSaleCostskeys.GROSS_AMOUNT_NEEDED_FROM_BUSINESS_SALE]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [BusinessSaleCostskeys.CURRENT_BUSINESS_VALUE]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [BusinessSaleCostskeys.WEALTH_GAP]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
  });
  const initialValue = data?.business_sale_costs || {
    [BusinessSaleCostskeys.FEDERAL_TAX_RATE]: '',
    [BusinessSaleCostskeys.STATE_TAX_RATE]: '',
    [BusinessSaleCostskeys.TRANSACTION_EXPENSE_PERCENTAGE_COSTS]: '',
    [BusinessSaleCostskeys.TRANSACTION_EXPENSE_DOLLARS]: '',
    [BusinessSaleCostskeys.GROSS_AMOUNT_NEEDED_FROM_BUSINESS_SALE]: '',
    [BusinessSaleCostskeys.CURRENT_BUSINESS_VALUE]: '',
    [BusinessSaleCostskeys.WEALTH_GAP]: '',
  };

  const setAutoCalculateField = (
    event: React.ChangeEvent<HTMLInputElement>,
    setFieldValue: (
      field: string,
      value: any,
      shouldValidate?: boolean
    ) => void,
    values: any
  ) => {
    const { name, value } = event.target;

    // Update the current field in the values object
    const updatedValues = {
      ...values,
      [name]: value,
    };

    // Perform calculations or updates based on the updatedValues
    const netAmountNeeded = data?.[
      FinancialGapAnalysisScreens.POST_EXIT_FINANCES
    ][PostExitFinancesKeys.NET_AMOUNT_NEEDED_TO_FUND].replaceAll(',', '');
    const federalTaxRate =
      updatedValues[BusinessSaleCostskeys.FEDERAL_TAX_RATE] / 100;
    const stateTaxRate =
      updatedValues[BusinessSaleCostskeys.STATE_TAX_RATE] / 100;
    const transactionExpensePercentage =
      updatedValues[
        BusinessSaleCostskeys.TRANSACTION_EXPENSE_PERCENTAGE_COSTS
      ] / 100;
    const transactionExpenseDollars = Number(
      updatedValues[
        BusinessSaleCostskeys.TRANSACTION_EXPENSE_DOLLARS
      ].replaceAll(',', '')
    );

    const totalTaxAndExpenses =
      federalTaxRate + stateTaxRate + transactionExpensePercentage;

    const grossAmountNeeded =
      netAmountNeeded / (1 - totalTaxAndExpenses) + transactionExpenseDollars;

    const wealthGap =
      grossAmountNeeded -
      Number(
        updatedValues[BusinessSaleCostskeys.CURRENT_BUSINESS_VALUE].replaceAll(
          ',',
          ''
        )
      );

    // Update relevant fields using setFieldValue
    setFieldValue(name, value); // Update the current field first

    // Update other dependent fields
    setFieldValue(
      BusinessSaleCostskeys.GROSS_AMOUNT_NEEDED_FROM_BUSINESS_SALE,
      formatCurrency(Number(grossAmountNeeded.toFixed()))
        .toString()
        .replace('$', '')
    );
    setFieldValue(
      BusinessSaleCostskeys.WEALTH_GAP,
      formatCurrency(Number(wealthGap.toFixed())).toString().replace('$', '')
    );
  };

  const handleSubmit = (values: any) => {
    nextStep(values);
  };

  return (
    <div className='flex flex-col '>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values, setFieldValue }) => (
          <Form className='pl-5 py-3 bg-white'>
            <h2 className='text-xl font-semibold mt-7 underline'>
              Business Sale Costs
            </h2>
            <div className='flex flex-col gap-6 h-[calc(100vh-25rem)] mt-12 overflow-auto scrollbar pr-4 text-[1rem]'>
              <div className='grid grid-cols-2 gap-x-10 items-end'>
                <FormikInput
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                  }}
                  name={`${BusinessSaleCostskeys.FEDERAL_TAX_RATE}`}
                  key={`${BusinessSaleCostskeys.FEDERAL_TAX_RATE}`}
                  label={getKeyTitle(BusinessSaleCostskeys.FEDERAL_TAX_RATE)}
                  labelClassName='!text-[1rem]'
                />
                <FormikInput
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                  }}
                  name={`${BusinessSaleCostskeys.STATE_TAX_RATE}`}
                  key={`${BusinessSaleCostskeys.STATE_TAX_RATE}`}
                  label={getKeyTitle(BusinessSaleCostskeys.STATE_TAX_RATE)}
                  labelClassName='!text-[1rem]'
                />
              </div>
              <div className='grid grid-cols-2 gap-x-10 items-end'>
                <FormikInput
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                  }}
                  className='!my-auto'
                  name={`${BusinessSaleCostskeys.TRANSACTION_EXPENSE_PERCENTAGE_COSTS}`}
                  key={`${BusinessSaleCostskeys.TRANSACTION_EXPENSE_PERCENTAGE_COSTS}`}
                  label={getKeyTitle(
                    BusinessSaleCostskeys.TRANSACTION_EXPENSE_PERCENTAGE_COSTS
                  )}
                  labelClassName='!text-[1rem]'
                />
                <FormikInput
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                    setFieldValue(
                      BusinessSaleCostskeys.TRANSACTION_EXPENSE_DOLLARS,
                      formatCurrency(
                        Number(event.target.value.replaceAll(',', ''))
                      )
                        .toString()
                        .replace('$', '')
                    );
                  }}
                  leadingIcon='$'
                  name={`${BusinessSaleCostskeys.TRANSACTION_EXPENSE_DOLLARS}`}
                  key={`${BusinessSaleCostskeys.TRANSACTION_EXPENSE_DOLLARS}`}
                  label={getKeyTitle(
                    BusinessSaleCostskeys.TRANSACTION_EXPENSE_DOLLARS
                  )}
                  labelClassName='!text-[1rem]'
                />
              </div>
              <div className='grid grid-cols-2 gap-x-10 items-end'>
                <FormikInput
                  leadingIcon='$'
                  disabled
                  className='!mb-0.5'
                  name={`${BusinessSaleCostskeys.GROSS_AMOUNT_NEEDED_FROM_BUSINESS_SALE}`}
                  key={`${BusinessSaleCostskeys.GROSS_AMOUNT_NEEDED_FROM_BUSINESS_SALE}`}
                  label={getKeyTitle(
                    BusinessSaleCostskeys.GROSS_AMOUNT_NEEDED_FROM_BUSINESS_SALE
                  )}
                  labelClassName='!text-[1rem]'
                />

                <FormikInput
                  leadingIcon='$'
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                    setFieldValue(
                      BusinessSaleCostskeys.CURRENT_BUSINESS_VALUE,
                      formatCurrency(
                        Number(event.target.value.replaceAll(',', ''))
                      )
                        .toString()
                        .replace('$', '')
                    );
                  }}
                  className='!mb-0.5'
                  name={`${BusinessSaleCostskeys.CURRENT_BUSINESS_VALUE}`}
                  key={`${BusinessSaleCostskeys.CURRENT_BUSINESS_VALUE}`}
                  label={getKeyTitle(
                    BusinessSaleCostskeys.CURRENT_BUSINESS_VALUE
                  )}
                  labelClassName='!text-[1rem]'
                />
              </div>
              <div className='grid grid-cols-2 gap-x-10 items-end'>
                <FormikInput
                  leadingIcon='$'
                  labelIcon2={
                    <div className='flex  items-center gap-2'>
                      <IoInformationCircle
                        id='todayInvestments'
                        className='text-blue-01'
                      />
                      <div className='relative'>
                        <Tooltip
                          anchorSelect='#todayInvestments'
                          place='left-start'
                          offset={0}
                          className=' !bg-gray-100 border !z-10  !w-[30rem] !font-[500]  !left-1 !top-8  !text-black-01 !text-[0.9rem] !rounded-lg'
                          classNameArrow='border-r  border-b  !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                        >
                          This Wealth Gap is the amount of money needed to fund
                          the post-transition life style that you have
                          indicated. It takes into consideration the total of
                          your current savings/investments and their growth and
                          your current business value.d to cash, If needed to
                          fund post transition living.
                        </Tooltip>
                      </div>
                    </div>
                  }
                  disabled
                  className='!mb-0.5'
                  name={`${BusinessSaleCostskeys.WEALTH_GAP}`}
                  key={`${BusinessSaleCostskeys.WEALTH_GAP}`}
                  label={getKeyTitle(BusinessSaleCostskeys.WEALTH_GAP)}
                  labelClassName='!text-[1rem]'
                />
              </div>
            </div>

            <div className='pr-7'>
              <BackNextComponent
                backStep={backStep}
                buttonType='submit'
                buttonText='Submit'
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={() => onSaveToDraftClick(values)}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default BusinessSaleCosts;
