import { Form, Formik } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import FormikCheckbox from '../../../shared-resources/components/CheckBox/FormikCheckbox';

interface Props {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  data: any;
  onSaveToDraftClick: (data: any) => void;
}
const BalanceSheetData3: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;

  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    own: yup.boolean().required(''),
    rent: yup.boolean().required('Please Choose any one Option'),
    own_options: yup.string().when('own', {
      is: true,
      then: (schema) => schema.required('Please Choose any one Option'),
      otherwise: (schema) =>
        schema.nullable().typeError('Must be a valid number'),
    }),
    rent_options: yup.string().when('rent', {
      is: true,
      then: (schema) => schema.required('Please Choose any one Option'),
      otherwise: (schema) =>
        schema.nullable().typeError('Must be a valid number'),
    }),

    own_fair_market_value: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .when('own', {
        is: true,
        then: (schema) => schema.required('Fair Market Value is Required'),
        otherwise: (schema) =>
          schema.nullable().typeError('Must be a valid number'),
      }),
    own_remaining_morgage: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .when('own', {
        is: true,
        then: (schema) => schema.required('Remaining Morgage is Required'),
        otherwise: (schema) =>
          schema.nullable().typeError('Must be a valid number'),
      }),
    rent_remaining_morgage: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .when('rent_options', {
        is: '2',
        then: (schema) => schema.required('Remaining Morgage  is Required'),
        otherwise: (schema) =>
          schema.nullable().typeError('Must be a valid number'),
      }),
    own_true_market_level_monthly_rent: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .when('own', {
        is: true,
        then: (schema) =>
          schema.required('True Market Level Monthly Rent is Required'),
        otherwise: (schema) =>
          schema.nullable().typeError('Must be a valid number'),
      }),
    own_curr_monthly_rent: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .when('own_options', {
        is: '2',
        then: (schema) => schema.required('Current Monthly Rent is Required'),
        otherwise: (schema) =>
          schema.nullable().typeError('Must be a valid number'),
      }),
    rent_monthly_rent: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .when('rent_options', {
        is: '1',
        then: (schema) => schema.required('Monthly  Rent is Required'),
        otherwise: (schema) =>
          schema.nullable().typeError('Must be a valid number'),
      }),
    rent_square_footage: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .when('rent', {
        is: true,
        then: (schema) => schema.required('Square Footage is Required'),
        otherwise: (schema) =>
          schema.nullable().typeError('Must be a valid number'),
      }),
    rent_curr_monthly_rent: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .when('rent_options', {
        is: '2',
        then: (schema) => schema.required('Current  Monthly Rent is Required'),
        otherwise: (schema) =>
          schema.nullable().typeError('Must be a valid number'),
      }),
    rent_true_market_level_monthly_rent: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .when('rent_options', {
        is: '2',
        then: (schema) =>
          schema.required('True Markert Level Monthly Rent is Required'),
        otherwise: (schema) =>
          schema.nullable().typeError('Must be a valid number'),
      }),
    rent_fair_market_value_of_company: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .when('rent_options', {
        is: '2',
        then: (schema) =>
          schema.required('Fair Market Value of Company is Required'),
        otherwise: (schema) =>
          schema.nullable().typeError('Must be a valid number'),
      }),
  });
  const initialValue = data?.balance_sheet_data_3 || {
    own: null,
    rent: null,
    own_fair_market_value: '',
    own_remaining_morgage: '',
    rent_remaining_morgage: '',
    own_options: '',
    own_true_market_level_monthly_rent: '',
    rent_true_market_level_monthly_rent: '',
    own_curr_monthly_rent: '',
    rent_curr_monthly_rent: '',
    rent_options: '',
    rent_monthly_rent: '',
    rent_square_footage: '',
    rent_fair_market_value_of_company: '',
  };

  const handleSubmit = (values: any) => {
    nextStep();
    setData(values);
  };

  return (
    <div className='flex flex-col '>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values, setFieldValue, setFieldTouched }) => (
          <Form className='flex flex-col justify-between pl-5 py-4 bg-white'>
            <div className='font-[500] h-[calc(100vh-18rem)] overflow-auto scrollbar pr-4'>
              <h2>Do you own or rent?</h2>
              <div className='flex gap-10 mt-4'>
                <FormikCheckbox
                  name='own'
                  className={`!h-4 items-center !mt-0 ${
                    values.own ? 'pointer-events-none' : ''
                  } `}
                  key='own'
                  text={<span>Own</span>}
                  valueChanged={() => {
                    setFieldValue('own', true);
                    setFieldValue('rent', false);
                    setTimeout(() => setFieldTouched('rent', true, true));
                    setFieldValue('rent_remaining_morgage', '');
                    setFieldValue('rent_true_market_level_monthly_rent', '');
                    setFieldValue('rent_curr_monthly_rent', '');
                    setFieldValue('rent_monthly_rent', '');
                    setFieldValue('rent_square_footage', '');
                    setFieldValue('rent_fair_market_value_of_company', '');
                    setFieldValue('rent_options', '');
                  }}
                />
                <FormikCheckbox
                  name='rent'
                  className={`!h-4 items-center !mt-0${
                    values.rent ? 'pointer-events-none' : ''
                  } `}
                  key='rent'
                  text={<span>Rent</span>}
                  valueChanged={() => {
                    setTimeout(() => setFieldTouched('own', true, true));
                    setFieldValue('rent', true);
                    setFieldValue('own', false);
                    setFieldValue('own_fair_market_value', '');
                    setFieldValue('own_remaining_morgage', '');
                    setFieldValue('own_true_market_level_monthly_rent', '');
                    setFieldValue('own_curr_monthly_rent', '');
                    setFieldValue('own_options', '');
                  }}
                />
              </div>

              {/* When selecting own */}
              {values.own && (
                <div className='mt-10'>
                  <div className='flex gap-10'>
                    <FormikInput
                      className='!h-12'
                      name='own_fair_market_value'
                      key='own_fair_market_value'
                      label='What is the fair market value of your property?'
                      labelClassName='mb-1'
                    />
                    <FormikInput
                      className='!h-12'
                      name='own_remaining_morgage'
                      key='own_remaining_morgage'
                      label='What is the remaining morgage on your property?'
                      labelClassName='mb-1'
                    />
                  </div>
                  <FormikRadio
                    name='own_options'
                    className='grid grid-cols-2'
                    labelClassName='!text-base'
                    options={[
                      {
                        value: '1',
                        label: 'The property is owned by this business entity.',
                      },
                      {
                        value: '2',
                        label:
                          'The property is owned by a 2nd entity with common ownership',
                      },
                    ]}
                  />
                  {values.own_options === '2' ? (
                    <div className='flex flex-col gap-3 mt-8 w-1/2 pr-5'>
                      <FormikInput
                        className='!h-12'
                        name='own_curr_monthly_rent'
                        key='own_curr_monthly_rent'
                        label='What is the current monthly rent paid to the 2nd entity?'
                        labelClassName='mb-1'
                      />
                      <FormikInput
                        className='!h-12'
                        name='own_true_market_level_monthly_rent'
                        key='own_true_market_level_monthly_rent'
                        label='What would the true market level monthly rent be?'
                        labelClassName='mb-1'
                      />
                    </div>
                  ) : (
                    values.own_options && (
                      <div className='flex flex-col gap-3 mt-8 w-1/2 pr-5'>
                        <FormikInput
                          className='!h-12'
                          name='own_true_market_level_monthly_rent'
                          key='own_true_market_level_monthly_rent'
                          label='What would the true market level monthly rent be?'
                          labelClassName='mb-1'
                        />
                      </div>
                    )
                  )}
                </div>
              )}

              {/* When selecting Rent */}
              {values.rent && (
                <div className='mt-10'>
                  <FormikRadio
                    name='rent_options'
                    className='grid grid-cols-2'
                    labelClassName='!text-base'
                    options={[
                      {
                        value: '1',
                        label: 'Rent paid to an independent 3rd party.',
                      },
                      {
                        value: '2',
                        label:
                          'Rent is paid to an entity that you own or are affiliated with.',
                      },
                    ]}
                  />
                  {values.rent_options === '2' ? (
                    <>
                      <div className='flex mt-8'>
                        <FormikInput
                          className='!h-12 !w-2/3'
                          name='rent_fair_market_value_of_company'
                          key='rent_fair_market_value_of_company'
                          label='What is the fair market value of your property?'
                          labelClassName='mb-1'
                        />
                        <FormikInput
                          className='!h-12 !w-2/3'
                          name='rent_remaining_morgage'
                          key='rent_remaining_morgage'
                          label='What is the remaining morgage on your property?'
                          labelClassName='mb-1'
                        />
                      </div>
                      <div className='flex'>
                        <FormikInput
                          className='!h-12 !w-2/3'
                          name='rent_curr_monthly_rent'
                          key='rent_curr_monthly_rent'
                          label='What is the current monthly rent you paying?'
                          labelClassName='mb-1'
                        />
                        <FormikInput
                          className='!h-12 !w-2/3'
                          name='rent_true_market_level_monthly_rent'
                          key='rent_true_market_level_monthly_rent'
                          label='What would the true market level monthly rent be?'
                          labelClassName='mb-1'
                        />
                      </div>
                      <div className='flex  pr-6'>
                        <FormikInput
                          className='!h-12 !w-1/3'
                          name='rent_square_footage'
                          key='rent_square_footage'
                          label='What is the square footage?'
                          labelClassName='mb-1'
                        />
                      </div>
                    </>
                  ) : (
                    values.rent_options && (
                      <div className='flex gap-8 mt-8 '>
                        <FormikInput
                          className='!h-12 !w-2/3'
                          name='rent_monthly_rent'
                          key='rent_monthly_rent'
                          label='What is your monthly rent?'
                          labelClassName='mb-1'
                        />
                        <FormikInput
                          className='!h-12 !w-2/3'
                          name='rent_square_footage'
                          key='rent_square_footage'
                          label='What is the square footage?'
                          labelClassName='mb-1'
                        />
                      </div>
                    )
                  )}
                </div>
              )}
            </div>

            <div className='pr-7'>
              <BackNextComponent
                backStep={backStep}
                buttonType='submit'
                buttonText='Submit'
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  onSaveToDraftClick(values);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default BalanceSheetData3;
