import { User } from 'models/entities/User';
import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import ConfirmYourAccountModal from 'shared-resources/components/BusinessOwner/Modals/ConfirmYourAccountModal';
import SetPasswordModal from 'shared-resources/components/BusinessOwner/Modals/SetPasswordModal';
import Button from 'shared-resources/components/Button/Button';
import Modal from 'shared-resources/components/Modal/Modal';
import { setPublicPasswordAction } from 'store/actions/forgot-password.action';
import { AssessmentTools, RouteKey, UserRouteType, UserType } from 'types/enum';
import thankYou from '../../assets/thankyou.svg';

interface Props {
  loggedInUserData?: User;
  token?: string | null;
  isPasswordSet?: boolean;
  pageContent?: string;
  businessOwnerProperties?: {
    [key: string]: any;
  };
}
const ThankYouPage: React.FC<Props> = ({
  loggedInUserData,
  token,
  isPasswordSet,
  pageContent,
  businessOwnerProperties,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { id } = useParams();

  const [confirmYourAccountModalVisible, setConfirmYourAccountModalVisibility] =
    useState<boolean>(false);
  const [setPasswordModalVisible, setSetPasswordModalVisibility] =
    useState<boolean>(false);

  const handleConfirmYourAccountModalVisibility = (value: boolean) => {
    setConfirmYourAccountModalVisibility(value);
  };
  const handleSetPasswordModalVisibility = (value: boolean) => {
    setSetPasswordModalVisibility(value);
  };
  const handleOnConfirmAccount = () => {
    setSetPasswordModalVisibility(true);
  };

  const onSubmitConfirmAccount = (values: boolean) => {
    if (values) {
      setSetPasswordModalVisibility(true);
      setConfirmYourAccountModalVisibility(false);
    }
  };
  const onSubmitSetPassword = (values: any) => {
    dispatch(
      setPublicPasswordAction({
        token: token as string,
        newPassword: values.newPassword,
        onSuccess: () => {
          setSetPasswordModalVisibility(false);
          navigate(`/${UserRouteType.BUSINESS_OWNER}/${RouteKey.LOGIN}`);
        },
      })
    );
  };

  // Function to determine which assessment was completed based on location
  const getAssessmentToolFromLocation = (
    tab: string
  ): AssessmentTools | null => {
    // Map the tab to the corresponding AssessmentTool
    switch (tab) {
      case RouteKey.READINESS_ASSESSMENT:
        return AssessmentTools.READINESS_ASSESSMENT;
      case RouteKey.TRANSITION_OBJECTIVES:
        return AssessmentTools.TRANSITION_OBJECTIVES;
      case RouteKey.OWNER_RELIANCE:
        return AssessmentTools.OWNER_RELIANCE;
      case RouteKey.BUSINESS_VALUATION:
        return AssessmentTools.BUSINESS_VALUATION;
      case RouteKey.BUSINESS_CONTINUITY:
        return AssessmentTools.BUSINESS_CONTINUITY;
      case RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES:
        return AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES;
      case RouteKey.FINANCIAL_GAP_ANALYSIS:
        return AssessmentTools.FINANCIAL_GAP_ANALYSIS;
      case RouteKey.STAKEHOLDER_ALIGNMENT_MEETING:
        return AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING;
      case RouteKey.ASSET_PROTECTION:
        return AssessmentTools.ASSET_PROTECTION;
      case RouteKey.BUYER_TYPE_DEAL_STRUCTURE:
        return AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE;
      default:
        return null;
    }
  };

  // Function to navigate back to the BusinessOwnerAssessment dashboard
  const handleBackToAssessment = () => {
    const lastIndex = location.pathname.lastIndexOf('/');
    const tab = location.pathname.substring(lastIndex + 1);

    // Navigate based on user type and assessment tool
    if (loggedInUserData?.type === UserType.ADVISOR && id) {
      // For advisors, navigate to the business owner details page with tab parameter
      navigate(
        `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${id}?tab=${tab}`
      );
    } else if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      const assessmentTool = getAssessmentToolFromLocation(tab);
      if (!assessmentTool) {
        // Fallback to dashboard if we can't determine the assessment
        navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`);
        return;
      }
      // Map assessment tools to their BusinessOwnerAssessment dashboard routes
      const toolToDashboardMap: Record<AssessmentTools, string> = {
        [AssessmentTools.READINESS_ASSESSMENT]: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.READINESS_ASSESSMENT_DASHBOARD}`,
        [AssessmentTools.TRANSITION_OBJECTIVES]: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.TRANSITION_OBJECTIVES_DASHBOARD}`,
        [AssessmentTools.OWNER_RELIANCE]: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.OWNER_RELIANCE_DASHBOARD}`,
        [AssessmentTools.BUSINESS_VALUATION]: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.BUSINESS_VALUATION_DASHBOARD}`,
        [AssessmentTools.BUSINESS_CONTINUITY]: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.BUSINESS_CONTINUITY_DASHBOARD}`,
        [AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES]: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES_DASHBOARD}`,
        [AssessmentTools.FINANCIAL_GAP_ANALYSIS]: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.FINANCIAL_GAP_ANALYSIS_DASHBOARD}`,
        [AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING]: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.STAKEHOLDER_ALIGNMENT_MEETING_DASHBOARD}`,
        [AssessmentTools.ASSET_PROTECTION]: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.ASSET_PROTECTION_DASHBOARD}`,
        [AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE]: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.BUYER_TYPE_DEAL_STRUCTURE_DASHBOARD}`,
      };

      const route = toolToDashboardMap[assessmentTool];
      if (route) {
        navigate(route);
      } else {
        navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`);
      }
    } else {
      // Fallback to dashboard
      navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`);
    }
  };
  return (
    <div className='rounded-xl border bg-white h-[calc(100vh-9.6875rem)] w-full flex flex-col justify-center items-center space-y-6'>
      <img src={thankYou} alt='Thank You' />
      <h1 className='text-lg font-semibold'>Thank You</h1>
      <h1 className='max-w-[50rem] px-2 text-center font-medium'>
        {pageContent}
      </h1>
      {location?.pathname?.includes('public') && !isPasswordSet && (
        <Button onClick={() => handleOnConfirmAccount()} className='px-6 py-2'>
          Confirm Your Account
        </Button>
      )}
      {location?.pathname?.includes('public') && (
        <Button
          onClick={() => navigate('/business-owner/login/')}
          className='px-6 py-2'
        >
          Back To Login
        </Button>
      )}
      <div className='space-x-10 flex items-center'>
        {!location?.pathname?.includes('public') && (
          <Button onClick={handleBackToAssessment} className='px-6 py-2'>
            Back To Dashboard
          </Button>
        )}
        {/* {loggedInUserData?.type === UserType.ADVISOR && (
          <Link
            className='text-blue-01 font-medium cursor-pointer'
            to={
              assessementType === 'Readiness'
                ? `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${id}/readiness-report`
                : `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${id}/transition-report`
            }
          >
            {viewReport && (
              <div className='flex items-center'>
                View Report <FaArrowUp className='rotate-45 ml-2' />
              </div>
            )}
          </Link>
        )} */}
      </div>
      <Modal
        visible={confirmYourAccountModalVisible}
        handleVisibility={handleConfirmYourAccountModalVisibility}
        closeOnOutsideClick
      >
        <ConfirmYourAccountModal
          handleConfirmYourAccountModalVisibility={
            handleConfirmYourAccountModalVisibility
          }
          onSubmitConfirmAccount={onSubmitConfirmAccount}
        />
      </Modal>
      <Modal
        visible={setPasswordModalVisible}
        title='Set Your Password'
        handleVisibility={handleSetPasswordModalVisibility}
        closeOnOutsideClick
      >
        <SetPasswordModal
          handleSetPasswordModalVisibility={handleSetPasswordModalVisibility}
          onSubmitSetPassword={onSubmitSetPassword}
          businessOwnerProperties={businessOwnerProperties}
        />
      </Modal>
    </div>
  );
};

export default ThankYouPage;
