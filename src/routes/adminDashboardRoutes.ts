import BusinessOwnersList from "shared-resources/components/BusinessOwner/BusinessOwnersList";
import { RouteKey, UserRouteType } from "types/enum";
import { SidebarRoutesConfigType } from "types/routes/routes.type";

export const adminDashboardRoutes: Array<SidebarRoutesConfigType> = [
  {
    name: 'Business Owner List',
    key: `/${UserRouteType.ADMIN}/${RouteKey.BO_LIST}`,
    component: BusinessOwnersList,
    props: {
    showSecondaryAdvisor: false,
    },
  },
  ];